import os
import pandas as pd
import numpy as np
import rasterio
from rasterio.enums import Resampling
from rasterio.warp import calculate_default_transform, reproject
from scipy.ndimage import distance_transform_edt, binary_opening, binary_closing, label
import warnings
from rasterio.errors import NotGeoreferencedWarning
import cv2
from tqdm import tqdm
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"ndvi_calculation_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)

warnings.filterwarnings("ignore", category=NotGeoreferencedWarning)
# =============================================================================
# PART A – PER-TILE PROCESSING: NDVI & DISTANCE MAPS
# =============================================================================

# =============================================================================
# IMAGE FILTERING FUNCTIONS
# =============================================================================

def detect_clouds(image_path, cloud_threshold=0.15):
    """
    Detects clouds in a satellite image using simple thresholding.

    Args:
        image_path (str): Path to the satellite image.
        cloud_threshold (float): Threshold for cloud detection (0-1).

    Returns:
        tuple: (cloud_percentage, is_cloudy) where is_cloudy is True if
               cloud_percentage > cloud_threshold
    """
    try:
        with rasterio.open(image_path) as src:
            # For Sentinel-2, band 1 (coastal aerosol) or band 10 (cirrus) can help detect clouds
            # Adjust based on your satellite data
            if src.count >= 10:  # If we have enough bands for Sentinel-2
                cirrus = src.read(10).astype('float32')
                # Normalize to 0-1 range
                cirrus_max = np.max(cirrus)
                if cirrus_max > 0:
                    cirrus_norm = cirrus / cirrus_max
                else:
                    cirrus_norm = cirrus

                # Simple thresholding for cloud detection
                cloud_mask = cirrus_norm > 0.1  # Adjust threshold as needed
                cloud_percentage = np.sum(cloud_mask) / cloud_mask.size

                return cloud_percentage, cloud_percentage > cloud_threshold
            else:
                # For other sensors or if cirrus band is not available
                # Use a simple brightness threshold on visible bands
                if src.count >= 3:  # If we have RGB bands
                    blue = src.read(2).astype('float32')
                    green = src.read(3).astype('float32')
                    red = src.read(4).astype('float32')

                    # Calculate brightness
                    brightness = (red + green + blue) / 3
                    brightness_norm = brightness / np.max(brightness) if np.max(brightness) > 0 else brightness

                    # Threshold for bright pixels (potential clouds)
                    cloud_mask = brightness_norm > 0.8  # Adjust threshold as needed
                    cloud_percentage = np.sum(cloud_mask) / cloud_mask.size

                    return cloud_percentage, cloud_percentage > cloud_threshold
                else:
                    # If we don't have enough bands, assume no clouds
                    return 0.0, False
    except Exception as e:
        logging.warning(f"Error detecting clouds in {image_path}: {e}")
        return 0.0, False

def check_ndvi_quality(ndvi_array, valid_range=(-1.0, 1.0), min_valid_pixels=0.8):
    """
    Checks the quality of an NDVI array.

    Args:
        ndvi_array (numpy.ndarray): NDVI array.
        valid_range (tuple): Valid range for NDVI values.
        min_valid_pixels (float): Minimum percentage of valid pixels required.

    Returns:
        tuple: (is_valid, quality_metrics) where quality_metrics is a dict with
               various quality metrics.
    """
    # Check for NaN or infinite values
    invalid_mask = ~np.isfinite(ndvi_array)
    invalid_percentage = np.sum(invalid_mask) / ndvi_array.size

    # Check for values outside valid range
    out_of_range = (ndvi_array < valid_range[0]) | (ndvi_array > valid_range[1])
    out_of_range_percentage = np.sum(out_of_range) / ndvi_array.size

    # Calculate statistics for valid pixels
    valid_mask = ~invalid_mask & ~out_of_range
    valid_percentage = np.sum(valid_mask) / ndvi_array.size

    if valid_percentage < min_valid_pixels:
        return False, {
            'valid_percentage': valid_percentage,
            'invalid_percentage': invalid_percentage,
            'out_of_range_percentage': out_of_range_percentage
        }

    # Calculate statistics
    if np.sum(valid_mask) > 0:
        valid_ndvi = ndvi_array[valid_mask]
        mean_ndvi = np.mean(valid_ndvi)
        std_ndvi = np.std(valid_ndvi)
        min_ndvi = np.min(valid_ndvi)
        max_ndvi = np.max(valid_ndvi)

        # Check for unusual statistics
        is_valid = True

        # Very low standard deviation might indicate sensor issues or homogeneous areas
        if std_ndvi < 0.01:
            is_valid = False

        # Extreme mean values might indicate calibration issues
        if mean_ndvi < -0.7 or mean_ndvi > 0.95:
            is_valid = False

        # Check for unusual distribution of NDVI values
        # Calculate percentiles to detect skewed distributions
        p10 = np.percentile(valid_ndvi, 10)
        p90 = np.percentile(valid_ndvi, 90)

        # If the range between 10th and 90th percentile is too small, the data might be too homogeneous
        if p90 - p10 < 0.1:
            is_valid = False

        return is_valid, {
            'valid_percentage': valid_percentage,
            'mean_ndvi': mean_ndvi,
            'std_ndvi': std_ndvi,
            'min_ndvi': min_ndvi,
            'max_ndvi': max_ndvi
        }
    else:
        return False, {
            'valid_percentage': valid_percentage,
            'invalid_percentage': invalid_percentage,
            'out_of_range_percentage': out_of_range_percentage
        }

def filter_extreme_ndvi_diff(ndvi_diff, min_threshold=-0.8, max_threshold=0.8, max_extreme_percentage=0.1):
    """
    Filters extreme NDVI differences.

    Args:
        ndvi_diff (numpy.ndarray): NDVI difference array.
        min_threshold (float): Minimum acceptable NDVI difference.
        max_threshold (float): Maximum acceptable NDVI difference.
        max_extreme_percentage (float): Maximum percentage of extreme values allowed.

    Returns:
        tuple: (is_valid, metrics) where metrics is a dict with statistics.
    """
    # Check for extreme values
    extreme_mask = (ndvi_diff < min_threshold) | (ndvi_diff > max_threshold)
    extreme_percentage = np.sum(extreme_mask) / ndvi_diff.size

    # Calculate statistics
    mean_diff = np.mean(ndvi_diff)
    std_diff = np.std(ndvi_diff)
    min_diff = np.min(ndvi_diff)
    max_diff = np.max(ndvi_diff)

    is_valid = extreme_percentage <= max_extreme_percentage

    return is_valid, {
        'extreme_percentage': extreme_percentage,
        'mean_diff': mean_diff,
        'std_diff': std_diff,
        'min_diff': min_diff,
        'max_diff': max_diff
    }

def check_spatial_coherence(mask, min_region_size=10, min_coherence=0.5):
    """
    Checks the spatial coherence of deforestation or growth regions.

    Args:
        mask (numpy.ndarray): Binary mask of deforestation or growth regions.
        min_region_size (int): Minimum size of a coherent region.
        min_coherence (float): Minimum ratio of coherent pixels to total pixels.

    Returns:
        bool: True if the mask has good spatial coherence.
    """
    # Apply morphological operations to remove noise
    cleaned_mask = binary_opening(mask, structure=np.ones((3, 3)))

    # Label connected components
    labeled_mask, num_regions = label(cleaned_mask)

    # Count pixels in each region
    region_sizes = np.bincount(labeled_mask.ravel())[1:] if num_regions > 0 else []

    # Count coherent pixels (in regions larger than min_region_size)
    coherent_pixels = np.sum([size for size in region_sizes if size >= min_region_size])
    total_pixels = np.sum(mask)

    if total_pixels == 0:
        return True  # No deforestation/growth pixels

    coherence_ratio = coherent_pixels / total_pixels

    return coherence_ratio >= min_coherence

def calculate_ndvi_from_image(image_path):
    """
    Calculates NDVI from a multi-band image and returns the NDVI array.

    Assumption: Band 1 is Red and Band 4 is NIR (adjust indices as needed).
    NDVI = (NIR - Red) / (NIR + Red + 1e-6)

    Returns the computed NDVI array without writing to disk.
    """
    with rasterio.open(image_path) as src:
        # Read bands as float32 (adjust indices for your sensor configuration)
        red = src.read(1).astype('float32')
        nir = src.read(4).astype('float32')

        # Compute NDVI while avoiding division by zero and handling edge cases
        np.seterr(divide='ignore', invalid='ignore')

        # Mask out invalid values (negative reflectance or extreme outliers)
        valid_mask = (red >= 0) & (nir >= 0) & (red < 10000) & (nir < 10000)

        # Initialize NDVI array with zeros
        ndvi = np.zeros_like(red, dtype='float32')

        # Calculate NDVI only for valid pixels
        denominator = nir[valid_mask] + red[valid_mask]
        ndvi[valid_mask] = np.where(
            denominator > 1e-6,
            (nir[valid_mask] - red[valid_mask]) / denominator,
            0
        )

        # Clip NDVI to valid range [-1, 1]
        ndvi = np.clip(ndvi, -1.0, 1.0)

        return ndvi

def calculate_ndvi_from_multiband(image_path, output_ndvi_path, target_crs="EPSG:4326"):
    """
    Calculates NDVI from a multi-band image and writes the NDVI GeoTIFF.

    Assumption: Band 1 is Red and Band 4 is NIR (adjust indices as needed).
    NDVI = (NIR - Red) / (NIR + Red + 1e-6)

    Returns the computed NDVI array.
    """
    with rasterio.open(image_path) as src:
        # Read bands as float32 (adjust indices for your sensor configuration)
        red = src.read(1).astype('float32')
        nir = src.read(4).astype('float32')

        # Compute NDVI while avoiding division by zero and handling edge cases
        np.seterr(divide='ignore', invalid='ignore')

        # Mask out invalid values (negative reflectance or extreme outliers)
        valid_mask = (red >= 0) & (nir >= 0) & (red < 10000) & (nir < 10000)

        # Initialize NDVI array with zeros
        ndvi = np.zeros_like(red, dtype='float32')

        # Calculate NDVI only for valid pixels
        denominator = nir[valid_mask] + red[valid_mask]
        ndvi[valid_mask] = np.where(
            denominator > 1e-6,
            (nir[valid_mask] - red[valid_mask]) / denominator,
            0
        )

        # Clip NDVI to valid range [-1, 1]
        ndvi = np.clip(ndvi, -1.0, 1.0)

        # Copy metadata and update for a single band output
        meta = src.meta.copy()
        meta.update(dtype='float32', count=1)

        # Reproject if source CRS differs from target_crs
        if src.crs != target_crs:
            transform, width, height = calculate_default_transform(
                src.crs, target_crs, src.width, src.height, *src.bounds)
            meta.update(crs=target_crs, transform=transform, width=width, height=height)

            ndvi_reprojected = np.zeros((height, width), dtype='float32')
            reproject(
                source=ndvi,
                destination=ndvi_reprojected,
                src_transform=src.transform,
                src_crs=src.crs,
                dst_transform=transform,
                dst_crs=target_crs,
                resampling=Resampling.nearest)
            ndvi = ndvi_reprojected

        # Write NDVI to GeoTIFF
        with rasterio.open(output_ndvi_path, "w", **meta) as dst:
            dst.write(ndvi, 1)
    return ndvi

def calculate_distance_map(ndvi, output_distance_path, threshold=0.5):
    """
    Creates a binary vegetation mask from NDVI using a threshold then computes the
    Euclidean distance transform. Saves the result as a GeoTIFF.
    """
    binary_map = (ndvi > threshold).astype(np.uint8)
    distance_map = distance_transform_edt(binary_map == 0)

    meta = {
        "driver": "GTiff",
        "height": distance_map.shape[0],
        "width": distance_map.shape[1],
        "count": 1,
        "dtype": "float32"
    }
    with rasterio.open(output_distance_path, "w", **meta) as dst:
        dst.write(distance_map, 1)
    return output_distance_path

def process_tile(row, output_dir, target_crs="EPSG:4326"):
    """
    Processes a single tile based on a CSV row. Calculates NDVI and the distance map.
    Expects the CSV row to contain: 'tile_id', 'image_path', 'forest', 'time_period'.
    Output files are saved in a subfolder for the given forest.
    Returns a dict with tile_id, forest, time_period, and output file paths.
    """
    tile_id = row['tile_id']
    image_path = row['image_path']
    # Clean forest name: remove spaces
    forest = row['forest'].replace(" ", "_")
    time_period = row['time_period']

    # Create forest-specific folder within the NDVI_Outputs_v2 directory
    forest_folder = os.path.join(output_dir, forest)
    os.makedirs(forest_folder, exist_ok=True)

    # Define output paths for individual NDVI files
    ndvi_output_path = os.path.join(forest_folder, f"{forest}_{time_period}_Tile_{tile_id}_NDVI.tif")
    distance_output_path = os.path.join(forest_folder, f"{forest}_{time_period}_Tile_{tile_id}_Distance.tif")

    if os.path.exists(image_path):
        # Calculate and save NDVI
        logging.info(f"Calculating NDVI for {row['forest']} tile {tile_id} {time_period}")
        ndvi_array = calculate_ndvi_from_multiband(image_path, ndvi_output_path, target_crs)

        # Calculate and save distance map
        calculate_distance_map(ndvi_array, distance_output_path, threshold=0.5)

        return {
            "tile_id": tile_id,
            "forest": forest,
            "time_period": time_period,
            "ndvi_path": ndvi_output_path,
            "distance_path": distance_output_path
        }
    else:
        logging.warning(f"File not found: {image_path}")
        return None

def process_tiles_from_csv(input_csv, output_dir, target_crs="EPSG:4326"):
    """
    Reads the combined forest filepaths CSV and processes each tile to calculate NDVI and
    distance maps. Saves a summary CSV of the processed results.
    """
    df = pd.read_csv(input_csv)
    results = []
    for _, row in df.iterrows():
        res = process_tile(row, output_dir, target_crs)
        if res is not None:
            results.append(res)
    results_df = pd.DataFrame(results)
    output_csv = os.path.join(output_dir, "Processed_Forest_Tiles.csv")
    results_df.to_csv(output_csv, index=False)
    print(f"Processed tile data saved to: {output_csv}")
    return results_df

# =============================================================================
# PART B – TEMPORAL COMPARISON: NDVI DIFFERENCE, TERNARY MASKS, & DISTANCE MAP PATH
# =============================================================================

def calculate_ndvi_difference_and_ternary_mask(ndvi_array1, ndvi_array2, diff_output_path, mask_output_path, loss_threshold=-0.15, gain_threshold=0.25):
    """
    Calculates the pixel-wise difference between two NDVI arrays and creates a ternary mask.

    Parameters:
    -----------
    ndvi_array1 : numpy.ndarray
        NDVI array for the first time period
    ndvi_array2 : numpy.ndarray
        NDVI array for the second time period
    diff_output_path : str
        Path to save the NDVI difference file
    mask_output_path : str
        Path to save the ternary mask file
    loss_threshold : float, optional
        Threshold for detecting vegetation loss (negative values)
    gain_threshold : float, optional
        Threshold for detecting vegetation gain (positive values)

    Ternary mask:
      +1 => Vegetation gain (NDVI increase beyond gain_threshold)
      -1 => Deforestation (NDVI decrease below loss_threshold)
       0 => Stable
    """
    ndvi_diff = ndvi_array2 - ndvi_array1

    # For debugging/analysis
    loss_percentile = np.percentile(ndvi_diff[ndvi_diff < loss_threshold], 50) if np.any(ndvi_diff < loss_threshold) else loss_threshold
    gain_percentile = np.percentile(ndvi_diff[ndvi_diff > gain_threshold], 50) if np.any(ndvi_diff > gain_threshold) else gain_threshold
    print(f"Tile NDVI diff stats - Min: {np.min(ndvi_diff):.3f}, Max: {np.max(ndvi_diff):.3f}, Mean: {np.mean(ndvi_diff):.3f}")
    print(f"Using loss threshold: {loss_threshold} (median of losses: {loss_percentile:.3f})")
    print(f"Using gain threshold: {gain_threshold} (median of gains: {gain_percentile:.3f})")

    ternary_mask = np.where(ndvi_diff > gain_threshold, 1,
                      np.where(ndvi_diff < loss_threshold, -1, 0))

    # Calculate deforestation percentage for metadata
    deforestation_pixels = np.sum(ndvi_diff < loss_threshold)
    total_pixels = ndvi_diff.size
    deforestation_percent = (deforestation_pixels / total_pixels) * 100

    # Calculate vegetation gain percentage for metadata
    gain_pixels = np.sum(ndvi_diff > gain_threshold)
    gain_percent = (gain_pixels / total_pixels) * 100

    print(f"Deforestation: {deforestation_percent:.2f}%, Vegetation gain: {gain_percent:.2f}%")

    # Create metadata with thresholds and statistics
    meta = {
        "driver": "GTiff",
        "height": ndvi_diff.shape[0],
        "width": ndvi_diff.shape[1],
        "count": 1,
        "dtype": "float32",
        "nodata": np.nan
    }

    # Add custom tags with analysis information
    tags = {
        "loss_threshold": str(loss_threshold),
        "gain_threshold": str(gain_threshold),
        "deforestation_percent": str(deforestation_percent),
        "gain_percent": str(gain_percent),
        "ndvi_diff_min": str(np.min(ndvi_diff)),
        "ndvi_diff_max": str(np.max(ndvi_diff)),
        "ndvi_diff_mean": str(np.mean(ndvi_diff))
    }

    with rasterio.open(diff_output_path, "w", **meta) as dst:
        dst.write(ndvi_diff, 1)
        dst.update_tags(**tags)

    meta.update(dtype='int16', nodata=-9999)
    with rasterio.open(mask_output_path, "w", **meta) as dst:
        dst.write(ternary_mask, 1)
        dst.update_tags(**tags)

    return diff_output_path, mask_output_path

def process_tile_pair(group, output_dir, target_crs="EPSG:4326",
                      time_period1="2015_2016", time_period2="2017_2018",
                      cloud_threshold=0.3, min_valid_pixels=0.7,
                      ndvi_diff_min_threshold=-0.8, ndvi_diff_max_threshold=0.8,
                      max_extreme_percentage=0.2, min_region_size=5, min_coherence=0.3):
    """
    Processes a tile pair (same forest, same tile_id) by comparing NDVI outputs from two time periods.
    Computes the NDVI difference and generates a ternary mask.
    Expects that the group contains at least one row for each specified time period.
    Outputs are saved in the forest subfolder.

    Additionally, it now retrieves the distance map path (from the first time period) and includes it
    in the returned dictionary.

    Args:
        group (pandas.DataFrame): Group of rows for the same forest and tile_id.
        output_dir (str): Output directory for saving results.
        target_crs (str): Target coordinate reference system.
        time_period1 (str): First time period.
        time_period2 (str): Second time period.

    Returns:
        dict or None: Dictionary with results, or None if processing failed.
    """
    forest = group.iloc[0]['forest']
    tile_id = group.iloc[0]['tile_id']

    # Find rows for the specified time periods.
    row1 = group[group['time_period'] == time_period1]
    row2 = group[group['time_period'] == time_period2]

    if row1.empty or row2.empty:
        logging.warning(f"Tile {tile_id} in {forest} missing one of the required time periods.")
        return None

    # Check if we have NDVI paths or need to calculate NDVI from image paths
    if 'ndvi_path' in row1.iloc[0]:
        # Use pre-calculated NDVI
        ndvi_path1 = row1.iloc[0]['ndvi_path']
        ndvi_path2 = row2.iloc[0]['ndvi_path']

        # Check if image paths are available for cloud detection
        if 'image_path' in row1.iloc[0]:
            image_path1 = row1.iloc[0]['image_path']
            image_path2 = row2.iloc[0]['image_path']

            # Check for clouds and filter based on cloud coverage
            cloud_percentage1, is_cloudy1 = detect_clouds(image_path1, cloud_threshold)
            cloud_percentage2, is_cloudy2 = detect_clouds(image_path2, cloud_threshold)

            # Log cloud coverage and filter if either image is too cloudy
            logging.info(f"Processing {forest} tile {tile_id} with cloud coverage: "
                       f"{time_period1}: {cloud_percentage1:.2%}, {time_period2}: {cloud_percentage2:.2%}")

            if is_cloudy1 or is_cloudy2:
                logging.warning(f"Skipping {forest} tile {tile_id} due to excessive cloud coverage")
                return None

        try:
            with rasterio.open(ndvi_path1) as src1:
                ndvi1 = src1.read(1).astype('float32')
            with rasterio.open(ndvi_path2) as src2:
                ndvi2 = src2.read(1).astype('float32')
        except Exception as e:
            logging.error(f"Error reading NDVI files for {forest} tile {tile_id}: {e}")
            return None
    else:
        # Calculate NDVI from image paths
        image_path1 = row1.iloc[0]['image_path']
        image_path2 = row2.iloc[0]['image_path']

        # Check for clouds and filter based on cloud coverage
        cloud_percentage1, is_cloudy1 = detect_clouds(image_path1, cloud_threshold)
        cloud_percentage2, is_cloudy2 = detect_clouds(image_path2, cloud_threshold)

        # Log cloud coverage and filter if either image is too cloudy
        logging.info(f"Processing {forest} tile {tile_id} with cloud coverage: "
                   f"{time_period1}: {cloud_percentage1:.2%}, {time_period2}: {cloud_percentage2:.2%}")

        if is_cloudy1 or is_cloudy2:
            logging.warning(f"Skipping {forest} tile {tile_id} due to excessive cloud coverage")
            return None

        logging.info(f"Calculating NDVI for {forest} tile {tile_id} {time_period1}")
        try:
            ndvi1 = calculate_ndvi_from_image(image_path1)
        except Exception as e:
            logging.error(f"Error calculating NDVI for {image_path1}: {e}")
            return None

        logging.info(f"Calculating NDVI for {forest} tile {tile_id} {time_period2}")
        try:
            ndvi2 = calculate_ndvi_from_image(image_path2)
        except Exception as e:
            logging.error(f"Error calculating NDVI for {image_path2}: {e}")
            return None

    # Check NDVI quality and filter based on quality
    is_valid1, metrics1 = check_ndvi_quality(ndvi1, min_valid_pixels=min_valid_pixels)
    is_valid2, metrics2 = check_ndvi_quality(ndvi2, min_valid_pixels=min_valid_pixels)

    # Log NDVI quality and filter if either NDVI is invalid
    logging.info(f"Processing {forest} tile {tile_id} with NDVI quality: {time_period1}={is_valid1}, {time_period2}={is_valid2}")

    if not is_valid1 or not is_valid2:
        logging.warning(f"Skipping {forest} tile {tile_id} due to poor NDVI quality")
        return None

    # Calculate NDVI difference
    ndvi_diff = ndvi2 - ndvi1

    # Check for extreme NDVI differences and filter based on them
    is_valid_diff, diff_metrics = filter_extreme_ndvi_diff(
        ndvi_diff,
        min_threshold=ndvi_diff_min_threshold,
        max_threshold=ndvi_diff_max_threshold,
        max_extreme_percentage=max_extreme_percentage
    )

    # Log extreme NDVI differences and filter if there are too many extreme differences
    logging.info(f"Processing {forest} tile {tile_id} with extreme NDVI differences: {diff_metrics}")

    if not is_valid_diff:
        logging.warning(f"Skipping {forest} tile {tile_id} due to excessive extreme NDVI differences")
        return None

    # Save outputs to the forest-specific folder
    try:
        forest_folder = os.path.join(output_dir, forest)
        os.makedirs(forest_folder, exist_ok=True)
        logging.info(f"Created forest folder: {forest_folder}")

        comparison_id = f"{time_period1}_to_{time_period2}"
        diff_output_path = os.path.join(forest_folder, f"{forest}_{comparison_id}_Tile_{tile_id}_NDVI_Diff.tif")
        mask_output_path = os.path.join(forest_folder, f"{forest}_{comparison_id}_Tile_{tile_id}_Ternary_Mask.tif")
        distance_output_path = os.path.join(forest_folder, f"{forest}_{comparison_id}_Tile_{tile_id}_Distance_Map.tif")

        logging.info(f"Output paths for {forest} tile {tile_id}:\n"
                    f"  NDVI diff: {diff_output_path}\n"
                    f"  Mask: {mask_output_path}\n"
                    f"  Distance map: {distance_output_path}")
    except Exception as e:
        logging.error(f"Error creating output paths for {forest} tile {tile_id}: {e}")
        return None

    # Get thresholds from global command line arguments
    import sys
    loss_threshold = -0.15  # Default value
    gain_threshold = 0.25   # Default value

    # Check if command line arguments include thresholds
    if '--loss_threshold' in sys.argv:
        idx = sys.argv.index('--loss_threshold')
        if idx + 1 < len(sys.argv):
            try:
                loss_threshold = float(sys.argv[idx + 1])
                logging.info(f"Using loss threshold from command line: {loss_threshold}")
            except:
                pass

    if '--gain_threshold' in sys.argv:
        idx = sys.argv.index('--gain_threshold')
        if idx + 1 < len(sys.argv):
            try:
                gain_threshold = float(sys.argv[idx + 1])
                logging.info(f"Using gain threshold from command line: {gain_threshold}")
            except:
                pass

    # Calculate NDVI difference and ternary mask
    diff_path, mask_path = calculate_ndvi_difference_and_ternary_mask(
        ndvi1, ndvi2, diff_output_path, mask_output_path,
        loss_threshold=loss_threshold,
        gain_threshold=gain_threshold)

    # Check spatial coherence of deforestation and growth regions and filter based on coherence
    with rasterio.open(mask_path) as src:
        ternary_mask = src.read(1)

        # Check deforestation regions (value -1)
        deforestation_mask = (ternary_mask == -1)
        deforestation_coherent = check_spatial_coherence(
            deforestation_mask,
            min_region_size=min_region_size,
            min_coherence=min_coherence
        )

        # Check growth regions (value 1)
        growth_mask = (ternary_mask == 1)
        growth_coherent = check_spatial_coherence(
            growth_mask,
            min_region_size=min_region_size,
            min_coherence=min_coherence
        )

        # Log spatial coherence and filter if either deforestation or growth regions are incoherent
        logging.info(f"Processing {forest} tile {tile_id} with spatial coherence: deforestation={deforestation_coherent}, growth={growth_coherent}")

        # Only filter if there are significant deforestation or growth regions
        deforestation_percentage = np.sum(deforestation_mask) / deforestation_mask.size
        growth_percentage = np.sum(growth_mask) / growth_mask.size

        if (deforestation_percentage > 0.05 and not deforestation_coherent) or \
           (growth_percentage > 0.05 and not growth_coherent):
            logging.warning(f"Skipping {forest} tile {tile_id} due to incoherent deforestation or growth regions")
            return None

    # Generate distance map
    try:
        calculate_distance_map(ndvi2, distance_output_path, threshold=0.5)
    except Exception as e:
        logging.error(f"Error generating distance map for {forest} tile {tile_id}: {e}")

    # Read the deforestation percentage from the file metadata
    deforestation_percent = 0.0
    try:
        with rasterio.open(diff_path) as src:
            if 'deforestation_percent' in src.tags():
                deforestation_percent = float(src.tags()['deforestation_percent'])
    except Exception as e:
        logging.error(f"Error reading deforestation percentage for {forest} tile {tile_id}: {e}")

    # Log success
    logging.info(f"Successfully processed {forest} tile {tile_id} for {comparison_id}")

    return {
        "tile_id": tile_id,
        "forest": forest,
        "time_period_comparison": comparison_id,
        "ndvi_diff_path": diff_path,
        "ternary_mask_path": mask_path,
        "distance_map_path": distance_output_path,
        "deforestation_percent": deforestation_percent,
        "quality_metrics": {
            **metrics1,
            **metrics2,
            **diff_metrics
        }
    }

def process_tile_pairs(processed_df, output_dir, target_crs="EPSG:4326",
                       time_period1="2015_2016", time_period2="2017_2018",
                       cloud_threshold=0.15, min_valid_pixels=0.8,
                       ndvi_diff_min_threshold=-0.8, ndvi_diff_max_threshold=0.8,
                       max_extreme_percentage=0.1, min_region_size=10, min_coherence=0.5,
                       use_parallel=True, max_workers=4):
    """
    Groups the processed tile DataFrame by forest and tile_id, then processes the pair of NDVI images
    for two specified time periods to generate NDVI differences and corresponding ternary masks.
    Saves a summary CSV of the results.

    Args:
        processed_df (pandas.DataFrame): DataFrame with processed tile information.
        output_dir (str): Output directory for saving results.
        target_crs (str): Target coordinate reference system.
        time_period1 (str): First time period.
        time_period2 (str): Second time period.
        cloud_threshold (float): Maximum acceptable cloud coverage.
        min_valid_pixels (float): Minimum percentage of valid pixels required in NDVI.
        ndvi_diff_min_threshold (float): Minimum acceptable NDVI difference.
        ndvi_diff_max_threshold (float): Maximum acceptable NDVI difference.
        max_extreme_percentage (float): Maximum percentage of extreme NDVI differences allowed.
        min_region_size (int): Minimum size of a coherent region in pixels.
        min_coherence (float): Minimum ratio of coherent pixels to total pixels.
        use_parallel (bool): Whether to use parallel processing.
        max_workers (int): Maximum number of worker processes to use.

    Returns:
        pandas.DataFrame: DataFrame with results.
    """
    groups = processed_df.groupby(["forest", "tile_id"])
    pair_results = []

    # Create a progress bar
    total_groups = len(groups)
    logging.info(f"Processing {total_groups} tile pairs for {time_period1} to {time_period2}")

    if use_parallel and total_groups > 1:
        # Process tiles in parallel
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Create a list of futures
            futures = []
            for _, group in groups:
                future = executor.submit(
                    process_tile_pair, group, output_dir, target_crs,
                    time_period1, time_period2, cloud_threshold, min_valid_pixels,
                    ndvi_diff_min_threshold, ndvi_diff_max_threshold,
                    max_extreme_percentage, min_region_size, min_coherence
                )
                futures.append(future)

            # Process results as they complete
            for future in tqdm(as_completed(futures), total=len(futures), desc=f"Processing {time_period1} to {time_period2}"):
                result = future.result()
                if result is not None:
                    pair_results.append(result)
    else:
        # Process tiles sequentially
        for _, group in tqdm(groups, desc=f"Processing {time_period1} to {time_period2}"):
            result = process_tile_pair(
                group, output_dir, target_crs,
                time_period1, time_period2, cloud_threshold, min_valid_pixels,
                ndvi_diff_min_threshold, ndvi_diff_max_threshold,
                max_extreme_percentage, min_region_size, min_coherence
            )
            if result is not None:
                pair_results.append(result)

    # Create DataFrame from results
    df_pairs = pd.DataFrame(pair_results) if pair_results else pd.DataFrame()

    # Save results to CSV
    if not df_pairs.empty:
        output_csv = os.path.join(output_dir, f"Deforestation_Data_{time_period1}_to_{time_period2}.csv")
        df_pairs.to_csv(output_csv, index=False)
        logging.info(f"Deforestation data saved to: {output_csv} ({len(df_pairs)} valid tile pairs)")
    else:
        logging.warning(f"No valid tile pairs found for {time_period1} to {time_period2}")

    return df_pairs

def process_all_time_period_pairs(processed_df, output_dir, target_crs="EPSG:4326", time_periods=None,
                              cloud_threshold=0.15, min_valid_pixels=0.8,
                              ndvi_diff_min_threshold=-0.8, ndvi_diff_max_threshold=0.8,
                              max_extreme_percentage=0.1, min_region_size=10, min_coherence=0.5,
                              use_parallel=True, max_workers=4):
    """
    Automates processing of all adjacent time period pairs based on a list of time periods.
    For instance, if time_periods = [ "2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024" ],
    it will compare:
       2015_2016 -> 2017_2018,
       2017_2018 -> 2019_2020,
       2019_2020 -> 2021_2022, and
       2021_2022 -> 2023_2024.
    All pair results are concatenated and saved as one CSV.

    Args:
        processed_df (pandas.DataFrame): DataFrame with processed tile information.
        output_dir (str): Output directory for saving results.
        target_crs (str): Target coordinate reference system.
        time_periods (list): List of time periods to process.
        cloud_threshold (float): Maximum acceptable cloud coverage.
        min_valid_pixels (float): Minimum percentage of valid pixels required in NDVI.
        ndvi_diff_min_threshold (float): Minimum acceptable NDVI difference.
        ndvi_diff_max_threshold (float): Maximum acceptable NDVI difference.
        max_extreme_percentage (float): Maximum percentage of extreme NDVI differences allowed.
        min_region_size (int): Minimum size of a coherent region in pixels.
        min_coherence (float): Minimum ratio of coherent pixels to total pixels.
        use_parallel (bool): Whether to use parallel processing.
        max_workers (int): Maximum number of worker processes to use.

    Returns:
        pandas.DataFrame: Combined DataFrame with results from all time period pairs.
    """
    if time_periods is None:
        time_periods = ["2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024"]

    logging.info(f"Processing {len(time_periods)-1} time period pairs")

    all_pairs = []
    for i in range(len(time_periods) - 1):
        tp1 = time_periods[i]
        tp2 = time_periods[i + 1]
        logging.info(f"Processing time period pair: {tp1} to {tp2}")

        df_pairs = process_tile_pairs(
            processed_df, output_dir, target_crs,
            time_period1=tp1, time_period2=tp2,
            cloud_threshold=cloud_threshold,
            min_valid_pixels=min_valid_pixels,
            ndvi_diff_min_threshold=ndvi_diff_min_threshold,
            ndvi_diff_max_threshold=ndvi_diff_max_threshold,
            max_extreme_percentage=max_extreme_percentage,
            min_region_size=min_region_size,
            min_coherence=min_coherence,
            use_parallel=use_parallel,
            max_workers=max_workers
        )

        if df_pairs is not None and not df_pairs.empty:
            all_pairs.append(df_pairs)
            logging.info(f"Added {len(df_pairs)} valid tile pairs for {tp1} to {tp2}")
        else:
            logging.warning(f"No valid tile pairs for {tp1} to {tp2}")

    if all_pairs:
        combined_df = pd.concat(all_pairs, ignore_index=True)
        combined_csv = os.path.join(output_dir, "Deforestation_Data_All_Pairs.csv")
        combined_df.to_csv(combined_csv, index=False)
        logging.info(f"All deforestation pair data saved to: {combined_csv} ({len(combined_df)} total tile pairs)")
        return combined_df
    else:
        logging.warning("No deforestation pair data produced.")
        return pd.DataFrame()

# =============================================================================
# MAIN FUNCTION
# =============================================================================

def main():
    import argparse

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Calculate NDVI differences and generate deforestation masks.')
    parser.add_argument('--input_csv', type=str, default=r"E:\Sentinelv3\Combined_Forest_FilePaths.csv",
                        help='Input CSV with processed tile information')
    parser.add_argument('--output_dir', type=str, default=r"E:\Sentinelv3\NDVI_Outputs_v2",
                        help='Output directory for NDVI difference files')
    parser.add_argument('--limit', type=int, default=None,
                        help='Limit the number of tile pairs to process (for testing)')
    parser.add_argument('--loss_threshold', type=float, default=-0.15,
                        help='NDVI difference threshold for deforestation detection')
    parser.add_argument('--gain_threshold', type=float, default=0.25,
                        help='NDVI difference threshold for vegetation gain detection')
    parser.add_argument('--tile_ids', type=str, default=None,
                        help='Comma-separated list of specific tile IDs to process (e.g., "27,36,45")')
    parser.add_argument('--skip_individual_ndvi', action='store_true',
                        help='Skip generating individual NDVI files')

    # Add parameters for quality metrics (used for filtering)
    parser.add_argument('--cloud_threshold', type=float, default=0.3,
                        help='Cloud coverage threshold for filtering (0-1)')
    parser.add_argument('--min_valid_pixels', type=float, default=0.7,
                        help='Minimum percentage of valid pixels required (0-1)')
    parser.add_argument('--ndvi_diff_min', type=float, default=-0.8,
                        help='Minimum acceptable NDVI difference')
    parser.add_argument('--ndvi_diff_max', type=float, default=0.8,
                        help='Maximum acceptable NDVI difference')
    parser.add_argument('--max_extreme_percentage', type=float, default=0.2,
                        help='Maximum percentage of extreme NDVI differences allowed (0-1)')
    parser.add_argument('--min_region_size', type=int, default=5,
                        help='Minimum size of a coherent region in pixels')
    parser.add_argument('--min_coherence', type=float, default=0.3,
                        help='Minimum ratio of coherent pixels to total pixels (0-1)')
    parser.add_argument('--parallel', action='store_true',
                        help='Use parallel processing')
    parser.add_argument('--max_workers', type=int, default=4,
                        help='Maximum number of worker processes to use')

    args = parser.parse_args()

    # Create output directory
    try:
        os.makedirs(args.output_dir, exist_ok=True)
        logging.info(f"Output directory created: {args.output_dir}")
    except Exception as e:
        logging.error(f"Error creating output directory: {e}")
        return

    # Load the processed tiles CSV
    if not os.path.exists(args.input_csv):
        logging.error(f"Error: Input CSV file not found: {args.input_csv}")
        return

    processed_df = pd.read_csv(args.input_csv)
    logging.info(f"Loaded {len(processed_df)} processed tiles from {args.input_csv}")

    # If limit is specified, limit the number of rows
    if args.limit:
        processed_df = processed_df.head(args.limit)
        logging.info(f"Limited to {args.limit} tiles for testing")

    # If specific tile IDs are provided, filter to only those tiles
    if args.tile_ids:
        tile_ids = [int(tid) for tid in args.tile_ids.split(',')]
        processed_df = processed_df[processed_df['tile_id'].isin(tile_ids)]
        logging.info(f"Filtered to specific tile IDs: {tile_ids}")

    # Log the filtering parameters
    logging.info(f"Using filtering parameters to remove problematic data:")
    logging.info(f"  Cloud threshold: {args.cloud_threshold} (tiles with higher cloud coverage will be filtered out)")
    logging.info(f"  Minimum valid pixels: {args.min_valid_pixels} (tiles with fewer valid pixels will be filtered out)")
    logging.info(f"  NDVI difference range: [{args.ndvi_diff_min}, {args.ndvi_diff_max}] (tiles with more extreme differences will be filtered out)")
    logging.info(f"  Maximum extreme percentage: {args.max_extreme_percentage} (tiles with more extreme pixels will be filtered out)")
    logging.info(f"  Minimum region size: {args.min_region_size} pixels (tiles with smaller deforestation/growth regions will be filtered out)")
    logging.info(f"  Minimum coherence: {args.min_coherence} (tiles with less coherent deforestation/growth will be filtered out)")
    logging.info(f"  Parallel processing: {args.parallel}")
    logging.info(f"  Maximum workers: {args.max_workers}")

    # PART A: Generate individual NDVI files for each satellite image
    if not args.skip_individual_ndvi:
        logging.info("STEP 1: Generating individual NDVI files for all satellite images...")

        # Process each row to generate individual NDVI files
        results = []

        if args.parallel:
            # Process in parallel
            with ProcessPoolExecutor(max_workers=args.max_workers) as executor:
                futures = []
                for _, row in processed_df.iterrows():
                    future = executor.submit(process_tile, row, args.output_dir, "EPSG:4326")
                    futures.append(future)

                # Process results as they complete
                for future in tqdm(as_completed(futures), total=len(futures), desc="Generating NDVI files"):
                    result = future.result()
                    if result is not None:
                        results.append(result)
        else:
            # Process sequentially
            for _, row in tqdm(processed_df.iterrows(), desc="Generating NDVI files"):
                result = process_tile(row, args.output_dir, "EPSG:4326")
                if result is not None:
                    results.append(result)

        # Save results to CSV
        if results:
            results_df = pd.DataFrame(results)
            output_csv = os.path.join(args.output_dir, "Individual_NDVI_Files.csv")
            results_df.to_csv(output_csv, index=False)
            logging.info(f"Individual NDVI file index saved to: {output_csv} ({len(results_df)} files)")
        else:
            logging.warning("No individual NDVI files were generated")
    else:
        logging.info("Skipping individual NDVI file generation as requested")

    # PART B: Process pairs for temporal comparisons.
    logging.info("STEP 2: Generating NDVI differences, ternary masks, and distance maps...")
    # This automatically processes all adjacent pairs as defined by the list.
    time_periods = ["2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024"]

    # Process all time period pairs with the specified filtering parameters
    process_all_time_period_pairs(
        processed_df,
        args.output_dir,
        target_crs="EPSG:4326",
        time_periods=time_periods,
        cloud_threshold=args.cloud_threshold,
        min_valid_pixels=args.min_valid_pixels,
        ndvi_diff_min_threshold=args.ndvi_diff_min,
        ndvi_diff_max_threshold=args.ndvi_diff_max,
        max_extreme_percentage=args.max_extreme_percentage,
        min_region_size=args.min_region_size,
        min_coherence=args.min_coherence,
        use_parallel=args.parallel,
        max_workers=args.max_workers
    )

    logging.info(f"NDVI data generation completed. Results saved to {args.output_dir}")
    logging.info(f"Run 'python deforestation_core/prepare_prediction_dataset.py' to update the prediction dataset")

if __name__ == "__main__":
    main()