import os
import pandas as pd
import numpy as np
import rasterio
from tqdm import tqdm
import glob
import warnings
from rasterio.errors import NotGeoreferencedWarning

# Suppress rasterio warnings
warnings.filterwarnings("ignore", category=NotGeoreferencedWarning)

def create_prediction_dataset(output_csv_path=r"E:\Sentinelv3\ModelReady\Deforestation_Prediction_Data.csv", ndvi_dir=None, input_csv=None):
    """
    Create a dataset for training a model to predict future deforestation.

    This dataset will use:
    - Current satellite images (T1) as input
    - NDVI images from time period T1
    - Ternary masks showing changes from T1 to T2 as target
    - Distance maps
    - NDVI difference maps

    Args:
        output_csv_path (str): Path to save the CSV file with the dataset information
        ndvi_dir (str): Directory containing NDVI outputs
        input_csv (str): Optional path to input CSV file with NDVI data information
    """
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)

    # Base directory for data
    base_dir = r"E:\Sentinelv3"

    # If ndvi_dir is not provided, extract it from the input_csv path
    if ndvi_dir is None and input_csv is not None:
        # Extract the directory from the input CSV path
        # E.g., E:\Sentinelv3\NDVI_Outputs_v3\Deforestation_Data_All_Pairs.csv -> E:\Sentinelv3\NDVI_Outputs_v3
        ndvi_dir = os.path.dirname(input_csv)
        print(f"Using NDVI directory from input CSV: {ndvi_dir}")
    elif ndvi_dir is None:
        # Default to NDVI_Outputs_v2 if no input_csv is provided
        ndvi_dir = r"E:\Sentinelv3\NDVI_Outputs_v2"
        print(f"Using default NDVI directory: {ndvi_dir}")

    # If input_csv is provided, use it to get the list of forests
    if input_csv and os.path.exists(input_csv):
        print(f"Using input CSV file: {input_csv}")
        # Read the CSV file
        input_df = pd.read_csv(input_csv)
        # Get unique forest names
        forest_dirs = input_df['forest'].unique().tolist()
        print(f"Found {len(forest_dirs)} forests in input CSV")
    else:
        # Find all forests in the NDVI directory
        forest_dirs = [d for d in os.listdir(ndvi_dir) if os.path.isdir(os.path.join(ndvi_dir, d)) and "Forest" in d]

    print(f"Found {len(forest_dirs)} forest directories")

    # Initialize lists to store data
    data = []

    # Initialize data collection

    # Process each forest
    for forest_name in tqdm(forest_dirs, desc="Processing forests"):
        print(f"\nProcessing forest: {forest_name}")
        # Get the forest directory in the raw satellite data
        forest_raw_dir = os.path.join(base_dir, forest_name)

        # Get the forest directory in the NDVI data
        forest_ndvi_dir = os.path.join(ndvi_dir, forest_name)
        print(f"Looking for ternary masks in: {forest_ndvi_dir}")

        # Find all ternary mask files for this forest
        # Try both with spaces and with underscores
        ternary_mask_files = glob.glob(os.path.join(forest_ndvi_dir, f"*Ternary_Mask.tif"))

        # If no files found, try with underscores
        if not ternary_mask_files:
            forest_name_underscore = forest_name.replace(' ', '_')
            forest_ndvi_dir_underscore = os.path.join(ndvi_dir, forest_name_underscore)
            if os.path.exists(forest_ndvi_dir_underscore):
                print(f"Trying underscore directory for {forest_name}: {forest_ndvi_dir_underscore}")
                ternary_mask_files = glob.glob(os.path.join(forest_ndvi_dir_underscore, f"*Ternary_Mask.tif"))

        print(f"Found {len(ternary_mask_files)} ternary mask files for {forest_name}")

        # Group files by time period pairs
        time_period_pairs = {}
        for mask_file in ternary_mask_files:
            # Extract time periods and tile ID from filename
            filename = os.path.basename(mask_file)
            parts = filename.split('_')

            # Standard format: Forest_2015_2016_to_2017_2018_Tile_27_Ternary_Mask.tif
            # Extract time periods and tile ID
            try:
                # Try standard format first
                time_period_t1 = f"{parts[1]}_{parts[2]}"
                time_period_t2 = f"{parts[4]}_{parts[5]}"
                tile_id = parts[7]
            except IndexError:
                # If that fails, try to extract based on pattern matching
                # Look for 'to' in the filename to split time periods
                to_index = parts.index('to') if 'to' in parts else -1
                if to_index > 0 and to_index + 2 < len(parts):
                    time_period_t1 = f"{parts[to_index-2]}_{parts[to_index-1]}"
                    time_period_t2 = f"{parts[to_index+1]}_{parts[to_index+2]}"
                    # Find 'Tile' in the parts
                    tile_index = parts.index('Tile') if 'Tile' in parts else -1
                    if tile_index > 0 and tile_index + 1 < len(parts):
                        tile_id = parts[tile_index+1]
                    else:
                        # Skip this file if we can't determine the tile ID
                        print(f"Skipping {filename} - cannot determine tile ID")
                        continue
                else:
                    # Skip this file if we can't determine the time periods
                    print(f"Skipping {filename} - cannot determine time periods")
                    continue

            # Create a key for this time period pair
            pair_key = f"{time_period_t1}_to_{time_period_t2}"

            # Add to dictionary
            if pair_key not in time_period_pairs:
                time_period_pairs[pair_key] = []

            time_period_pairs[pair_key].append((tile_id, mask_file))

        # Process each time period pair
        for pair_key, tile_files in time_period_pairs.items():
            # Extract time periods
            time_periods = pair_key.split('_to_')
            time_period_t1 = time_periods[0]
            time_period_t2 = time_periods[1]

            print(f"Processing {forest_name}: {time_period_t1} to {time_period_t2} ({len(tile_files)} tiles)")

            # Process each tile
            for tile_id, ternary_mask_path in tile_files:
                # Find the corresponding satellite image for T1 and T2
                # Format: Forest_2015_2016_Tile_027.tif
                # Pad tile ID with zeros if needed
                padded_tile_id = tile_id.zfill(3)

                # Use the Combined_Forest_FilePaths.csv to get the correct satellite image paths
                t1_tile_path = None
                t2_tile_path = None

                # Load the Combined_Forest_FilePaths.csv if not already loaded
                if 'combined_paths_df' not in locals():
                    combined_paths_file = os.path.join(base_dir, "Combined_Forest_FilePaths.csv")
                    if os.path.exists(combined_paths_file):
                        combined_paths_df = pd.read_csv(combined_paths_file)
                        print(f"Loaded Combined_Forest_FilePaths.csv with {len(combined_paths_df)} rows")
                    else:
                        combined_paths_df = None
                        print(f"Warning: Combined_Forest_FilePaths.csv not found at {combined_paths_file}")

                # Try to find the satellite image paths in the Combined_Forest_FilePaths.csv
                if combined_paths_df is not None:
                    # Find the T1 satellite image
                    t1_matches = combined_paths_df[
                        (combined_paths_df['forest'] == forest_name) &
                        (combined_paths_df['time_period'] == time_period_t1) &
                        (combined_paths_df['tile_id'].astype(str) == str(int(tile_id)))
                    ]

                    # Find the T2 satellite image
                    t2_matches = combined_paths_df[
                        (combined_paths_df['forest'] == forest_name) &
                        (combined_paths_df['time_period'] == time_period_t2) &
                        (combined_paths_df['tile_id'].astype(str) == str(int(tile_id)))
                    ]

                    if len(t1_matches) > 0:
                        t1_tile_path = t1_matches.iloc[0]['image_path']
                        print(f"Found T1 satellite image in Combined_Forest_FilePaths.csv: {t1_tile_path}")

                    if len(t2_matches) > 0:
                        t2_tile_path = t2_matches.iloc[0]['image_path']
                        print(f"Found T2 satellite image in Combined_Forest_FilePaths.csv: {t2_tile_path}")

                # If paths not found in the CSV, try different naming patterns
                if t1_tile_path is None or t2_tile_path is None:
                    print(f"Satellite image paths not found in Combined_Forest_FilePaths.csv for {forest_name} tile {tile_id}, trying different patterns")

                    # Define possible forest name formats
                    forest_name_formats = []

                    # Special cases for different forests
                    if forest_name == "Sam Houston Forest":
                        forest_name_formats.append("Sam_Houston")
                        forest_name_formats.append("Sam")
                    elif forest_name == "Fazenda Forest":
                        forest_name_formats.append("Fazenda_Manna")
                        forest_name_formats.append("Fazenda")
                    elif forest_name == "Rio Aruana Forest":
                        forest_name_formats.append("Rio_Aruana")
                        forest_name_formats.append("Rio")
                    else:
                        # Try with full forest name with underscores
                        forest_name_formats.append(forest_name.replace(' ', '_'))
                        # Try with just the first part of the forest name
                        forest_name_formats.append(forest_name.split(' ')[0])

                    # Try each forest name format
                    for forest_format in forest_name_formats:
                        t1_dir_name = f"{forest_format}_{time_period_t1}"
                        t2_dir_name = f"{forest_format}_{time_period_t2}"

                        # Try standard path format
                        candidate_t1_path = os.path.join(forest_raw_dir, t1_dir_name, f"{t1_dir_name}_Tile_{padded_tile_id}.tif")
                        candidate_t2_path = os.path.join(forest_raw_dir, t2_dir_name, f"{t2_dir_name}_Tile_{padded_tile_id}.tif")

                        if os.path.exists(candidate_t1_path) and t1_tile_path is None:
                            t1_tile_path = candidate_t1_path
                            print(f"Found T1 satellite image: {t1_tile_path}")

                        if os.path.exists(candidate_t2_path) and t2_tile_path is None:
                            t2_tile_path = candidate_t2_path
                            print(f"Found T2 satellite image: {t2_tile_path}")

                        # If both paths are found, break the loop
                        if t1_tile_path is not None and t2_tile_path is not None:
                            break

                # If still not found, use the default paths (will be marked as missing later)
                if t1_tile_path is None:
                    default_forest_name = forest_name.split(' ')[0]
                    t1_dir_name = f"{default_forest_name}_{time_period_t1}"
                    t1_tile_path = os.path.join(forest_raw_dir, t1_dir_name, f"{t1_dir_name}_Tile_{padded_tile_id}.tif")
                    print(f"Using default T1 path: {t1_tile_path}")

                if t2_tile_path is None:
                    default_forest_name = forest_name.split(' ')[0]
                    t2_dir_name = f"{default_forest_name}_{time_period_t2}"
                    t2_tile_path = os.path.join(forest_raw_dir, t2_dir_name, f"{t2_dir_name}_Tile_{padded_tile_id}.tif")
                    print(f"Using default T2 path: {t2_tile_path}")

                # Find the distance map
                distance_map_path = ternary_mask_path.replace("Ternary_Mask.tif", "Distance_Map.tif")

                # Find the NDVI difference
                ndvi_diff_path = ternary_mask_path.replace("Ternary_Mask.tif", "NDVI_Diff.tif")

                # Check if all required files exist
                missing_files = []
                if not os.path.exists(t1_tile_path):
                    missing_files.append(f"T1 satellite image: {t1_tile_path}")
                if not os.path.exists(t2_tile_path):
                    missing_files.append(f"T2 satellite image: {t2_tile_path}")
                if not os.path.exists(distance_map_path):
                    missing_files.append(f"Distance map: {distance_map_path}")
                if not os.path.exists(ndvi_diff_path):
                    missing_files.append(f"NDVI difference: {ndvi_diff_path}")

                # Extremely lenient filtering - include the tile if either the ternary mask OR the T1 satellite image exists
                if not os.path.exists(ternary_mask_path) and not os.path.exists(t1_tile_path):
                    print(f"Skipping {forest_name} tile {tile_id} due to missing both ternary mask and T1 satellite image")
                    print(f"  - Ternary mask: {ternary_mask_path}")
                    print(f"  - T1 satellite: {t1_tile_path}")
                    continue

                # Process all tiles equally without special handling

                # If we get here, at least one of the required files exists
                if not os.path.exists(ternary_mask_path):
                    print(f"Warning: Including {forest_name} tile {tile_id} without ternary mask")
                if not os.path.exists(t1_tile_path):
                    print(f"Warning: Including {forest_name} tile {tile_id} without T1 satellite image")

                # Log warning for non-critical missing files
                if missing_files:
                    print(f"Warning: Including {forest_name} tile {tile_id} with some missing files:")
                    for file in missing_files:
                        print(f"  - {file}")

                # Find the NDVI images for T1 and T2
                # Try both with spaces and with underscores
                forest_name_underscore = forest_name.replace(' ', '_')

                # First try with spaces
                ndvi_t1_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period_t1}_Tile_{tile_id}_NDVI.tif")
                ndvi_t2_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period_t2}_Tile_{tile_id}_NDVI.tif")

                # If not found, try with underscores
                if not os.path.exists(ndvi_t1_path):
                    ndvi_t1_path = os.path.join(ndvi_dir, forest_name_underscore, f"{forest_name_underscore}_{time_period_t1}_Tile_{tile_id}_NDVI.tif")

                if not os.path.exists(ndvi_t2_path):
                    ndvi_t2_path = os.path.join(ndvi_dir, forest_name_underscore, f"{forest_name_underscore}_{time_period_t2}_Tile_{tile_id}_NDVI.tif")

                # Check if NDVI files exist
                if not os.path.exists(ndvi_t1_path):
                    print(f"Warning: NDVI T1 file not found: {ndvi_t1_path}")
                    # Try alternative locations
                    alt_ndvi_dir = r"E:\Sentinelv3\NDVI_Outputs_Improved"
                    alt_ndvi_t1_path = os.path.join(alt_ndvi_dir, forest_name, f"{forest_name}_{time_period_t1}_Tile_{tile_id}_NDVI.tif")
                    if os.path.exists(alt_ndvi_t1_path):
                        print(f"Found alternative NDVI T1 file: {alt_ndvi_t1_path}")
                        ndvi_t1_path = alt_ndvi_t1_path

                if not os.path.exists(ndvi_t2_path):
                    print(f"Warning: NDVI T2 file not found: {ndvi_t2_path}")
                    # Try alternative locations
                    alt_ndvi_dir = r"E:\Sentinelv3\NDVI_Outputs_Improved"
                    alt_ndvi_t2_path = os.path.join(alt_ndvi_dir, forest_name, f"{forest_name}_{time_period_t2}_Tile_{tile_id}_NDVI.tif")
                    if os.path.exists(alt_ndvi_t2_path):
                        print(f"Found alternative NDVI T2 file: {alt_ndvi_t2_path}")
                        ndvi_t2_path = alt_ndvi_t2_path

                # Add to dataset
                data.append({
                    'forest': forest_name,
                    'time_period_t1': time_period_t1,
                    'time_period_t2': time_period_t2,
                    'tile_id': tile_id,
                    'satellite_path_t1': t1_tile_path,
                    'satellite_path_t2': t2_tile_path,
                    'ndvi_path_t1': ndvi_t1_path,
                    'ndvi_path_t2': ndvi_t2_path,
                    'ternary_mask_path': ternary_mask_path,
                    'distance_map_path': distance_map_path,
                    'ndvi_diff_path': ndvi_diff_path
                })

    # No special handling for any specific forest tiles

    # Create DataFrame
    df = pd.DataFrame(data)

    # Save to CSV
    df.to_csv(output_csv_path, index=False)

    print(f"Dataset created with {len(df)} samples")
    print(f"Saved to {output_csv_path}")

    return df

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Prepare dataset for deforestation prediction')
    parser.add_argument('--input_csv', type=str, default=r"E:\Sentinelv3\NDVI_Outputs_v2\Deforestation_Data_All_Pairs.csv",
                        help='Path to input CSV file with NDVI data information')
    parser.add_argument('--output_dir', type=str, default=r"E:\Sentinelv3\ModelReady",
                        help='Directory to save the processed dataset')
    args = parser.parse_args()

    # Create output path
    output_csv_path = os.path.join(args.output_dir, "Deforestation_Prediction_Data.csv")

    # Create the prediction dataset
    df = create_prediction_dataset(output_csv_path=output_csv_path, input_csv=args.input_csv)

    # Print some statistics
    print("\nDataset Statistics:")
    if len(df) > 0:
        print(f"Number of forests: {df['forest'].nunique()}")
        print(f"Number of time periods: {df['time_period_t1'].nunique()}")
        print(f"Number of tiles: {df['tile_id'].nunique()}")
    else:
        print("Dataset is empty. No statistics available.")

    # Check for missing files and filter out entries with missing satellite images
    if len(df) > 0:
        missing_files = 0
        rows_to_drop = []

        # First, check if satellite images exist
        for idx, row in df.iterrows():
            has_missing_satellite = False

            # Check satellite images first
            for key in ['satellite_path_t1', 'satellite_path_t2']:
                if not os.path.exists(row[key]):
                    missing_files += 1
                    print(f"Missing satellite image: {row[key]}")
                    has_missing_satellite = True

            if has_missing_satellite:
                rows_to_drop.append(idx)

        # Drop rows with missing satellite images
        if rows_to_drop:
            print(f"\nRemoving {len(rows_to_drop)} entries with missing satellite images")
            df = df.drop(rows_to_drop)
            print(f"Remaining dataset has {len(df)} samples")

        # Now check other files for the remaining rows
        missing_other_files = 0
        for idx, row in df.iterrows():
            for key in ['ndvi_path_t1', 'ndvi_path_t2', 'ternary_mask_path', 'distance_map_path', 'ndvi_diff_path']:
                if not os.path.exists(row[key]):
                    missing_other_files += 1
                    print(f"Missing file: {row[key]}")

        # Save the filtered CSV
        filtered_csv_path = os.path.join(os.path.dirname(output_csv_path), "Deforestation_Prediction_Data_Filtered.csv")
        df.to_csv(filtered_csv_path, index=False)
        print(f"Saved filtered dataset with {len(df)} samples to {filtered_csv_path}")

        if missing_files == 0 and missing_other_files == 0:
            print("All files exist!")
        else:
            print(f"Found {missing_files} missing satellite images and {missing_other_files} missing other files")
    else:
        print("No files to check since dataset is empty.")
