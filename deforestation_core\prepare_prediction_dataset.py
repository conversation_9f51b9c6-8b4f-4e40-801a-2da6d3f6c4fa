import os
import pandas as pd
import numpy as np
import rasterio
from tqdm import tqdm
import glob
import warnings
from rasterio.errors import NotGeoreferencedWarning

# Suppress rasterio warnings
warnings.filterwarnings("ignore", category=NotGeoreferencedWarning)

def create_prediction_dataset(output_csv_path=None, ndvi_dir=None, input_csv=None, base_dir=None):
    """
    Create a dataset for training a model to predict future deforestation using sequences of time periods.

    This dataset will use:
    - Satellite images from all 5 time periods (T1, T2, T3, T4, T5) for each tile
    - NDVI images from all 5 time periods
    - Ternary masks showing changes between consecutive time periods
    - Distance maps
    - NDVI difference maps

    Args:
        output_csv_path (str): Path to save the CSV file with the dataset information
        ndvi_dir (str): Directory containing NDVI outputs
        input_csv (str): Optional path to input CSV file with NDVI data information
        base_dir (str): Base directory for all data (defaults to E:\Sentinelv3)
    """
    # Set base directory
    if base_dir is None:
        base_dir = r"E:\Sentinelv3"

    print(f"Using base directory: {base_dir}")

    # Create organized subdirectories
    model_ready_dir = os.path.join(base_dir, "ModelReady")
    os.makedirs(model_ready_dir, exist_ok=True)

    # Set default output path if not provided
    if output_csv_path is None:
        output_csv_path = os.path.join(model_ready_dir, "Deforestation_Prediction_Data_Sequence.csv")

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)

    # If ndvi_dir is not provided, use default location in base directory
    if ndvi_dir is None:
        ndvi_dir = os.path.join(base_dir, "NDVI_Outputs_v2")
        print(f"Using default NDVI directory: {ndvi_dir}")

    # Create NDVI output directory if it doesn't exist
    os.makedirs(ndvi_dir, exist_ok=True)

    # If input_csv is provided, use it to get the list of forests
    if input_csv and os.path.exists(input_csv):
        print(f"Using input CSV file: {input_csv}")
        # Read the CSV file
        input_df = pd.read_csv(input_csv)
        # Get unique forest names
        forest_dirs = input_df['forest'].unique().tolist()
        print(f"Found {len(forest_dirs)} forests in input CSV")
    else:
        # Find all forests in the NDVI directory
        try:
            forest_dirs = [d for d in os.listdir(ndvi_dir) if os.path.isdir(os.path.join(ndvi_dir, d)) and "Forest" in d]
        except FileNotFoundError:
            print(f"Warning: NDVI directory {ndvi_dir} not found. Using default forest list.")
            # Use a default list of forests
            forest_dirs = ["Braunlage Forest", "Fazenda Forest", "Iracema Forest", "Rio Aruana Forest", "Sam Houston Forest"]

    print(f"Found {len(forest_dirs)} forest directories")

    # Initialize lists to store data
    data = []

    # Define all time periods
    all_time_periods = ["2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024"]

    # Load the Combined_Forest_FilePaths.csv for satellite image paths
    combined_paths_file = os.path.join(base_dir, "Combined_Forest_FilePaths.csv")
    combined_paths_df = None
    if os.path.exists(combined_paths_file):
        combined_paths_df = pd.read_csv(combined_paths_file)
        print(f"Loaded Combined_Forest_FilePaths.csv with {len(combined_paths_df)} rows")
    else:
        print(f"Warning: Combined_Forest_FilePaths.csv not found at {combined_paths_file}")

    # Process each forest
    for forest_name in tqdm(forest_dirs, desc="Processing forests"):
        print(f"\nProcessing forest: {forest_name}")
        # Get the forest directory in the raw satellite data
        forest_raw_dir = os.path.join(base_dir, forest_name)

        # Get the forest directory in the NDVI data
        forest_ndvi_dir = os.path.join(ndvi_dir, forest_name)
        print(f"Looking for NDVI files in: {forest_ndvi_dir}")

        # Try both with spaces and with underscores for the forest name
        forest_name_underscore = forest_name.replace(' ', '_')
        forest_ndvi_dir_underscore = os.path.join(ndvi_dir, forest_name_underscore)

        # Find all tile IDs for this forest
        all_tile_ids = set()

        # First check in the Combined_Forest_FilePaths.csv
        if combined_paths_df is not None:
            forest_tiles = combined_paths_df[combined_paths_df['forest'] == forest_name]['tile_id'].unique()
            all_tile_ids.update([str(tile_id) for tile_id in forest_tiles])
            print(f"Found {len(all_tile_ids)} unique tile IDs for {forest_name} in Combined_Forest_FilePaths.csv")

        # Also check for NDVI files to find more tile IDs
        for time_period in all_time_periods:
            # Check with spaces in forest name
            ndvi_pattern = os.path.join(forest_ndvi_dir, f"{forest_name}_{time_period}_Tile_*_NDVI.tif")
            ndvi_files = glob.glob(ndvi_pattern)

            # If no files found, try with underscores
            if not ndvi_files and os.path.exists(forest_ndvi_dir_underscore):
                ndvi_pattern = os.path.join(forest_ndvi_dir_underscore, f"{forest_name_underscore}_{time_period}_Tile_*_NDVI.tif")
                ndvi_files = glob.glob(ndvi_pattern)

            # Extract tile IDs from filenames
            for ndvi_file in ndvi_files:
                filename = os.path.basename(ndvi_file)
                parts = filename.split('_')
                try:
                    # Find 'Tile' in the parts
                    tile_index = parts.index('Tile') if 'Tile' in parts else -1
                    if tile_index > 0 and tile_index + 1 < len(parts):
                        tile_id = parts[tile_index+1]
                        all_tile_ids.add(tile_id)
                except:
                    continue

        print(f"Found {len(all_tile_ids)} unique tile IDs for {forest_name}")

        # Process each tile
        for tile_id in all_tile_ids:
            # Pad tile ID with zeros if needed
            padded_tile_id = tile_id.zfill(3)

            # Initialize paths for all time periods
            satellite_paths = {}
            ndvi_paths = {}
            ternary_mask_paths = {}
            distance_map_paths = {}
            ndvi_diff_paths = {}

            # Process each time period
            for i, time_period in enumerate(all_time_periods):
                # Initialize paths for this time period
                satellite_paths[time_period] = None
                ndvi_paths[time_period] = None

                # Find satellite image path for this time period
                if combined_paths_df is not None:
                    # Find the satellite image in the CSV
                    matches = combined_paths_df[
                        (combined_paths_df['forest'] == forest_name) &
                        (combined_paths_df['time_period'] == time_period) &
                        (combined_paths_df['tile_id'].astype(str) == str(int(tile_id)))
                    ]

                    if len(matches) > 0:
                        satellite_paths[time_period] = matches.iloc[0]['image_path']

                # If path not found in CSV, try different naming patterns
                if satellite_paths[time_period] is None:
                    # Define possible forest name formats
                    forest_name_formats = []

                    # Special cases for different forests
                    if forest_name == "Sam Houston Forest":
                        forest_name_formats.append("Sam_Houston")
                        forest_name_formats.append("Sam")
                    elif forest_name == "Fazenda Forest":
                        forest_name_formats.append("Fazenda_Manna")
                        forest_name_formats.append("Fazenda")
                    elif forest_name == "Rio Aruana Forest":
                        forest_name_formats.append("Rio_Aruana")
                        forest_name_formats.append("Rio")
                    else:
                        # Try with full forest name with underscores
                        forest_name_formats.append(forest_name.replace(' ', '_'))
                        # Try with just the first part of the forest name
                        forest_name_formats.append(forest_name.split(' ')[0])

                    # Try each forest name format
                    for forest_format in forest_name_formats:
                        dir_name = f"{forest_format}_{time_period}"

                        # Try standard path format
                        candidate_path = os.path.join(forest_raw_dir, dir_name, f"{dir_name}_Tile_{padded_tile_id}.tif")

                        if os.path.exists(candidate_path):
                            satellite_paths[time_period] = candidate_path
                            break

                # If still not found, use a default path (will be marked as missing later)
                if satellite_paths[time_period] is None:
                    default_forest_name = forest_name.split(' ')[0]
                    dir_name = f"{default_forest_name}_{time_period}"
                    satellite_paths[time_period] = os.path.join(forest_raw_dir, dir_name, f"{dir_name}_Tile_{padded_tile_id}.tif")

                # Find NDVI image for this time period
                # Try with spaces in forest name
                ndvi_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period}_Tile_{tile_id}_NDVI.tif")

                # If not found, try with underscores
                if not os.path.exists(ndvi_path):
                    ndvi_path = os.path.join(ndvi_dir, forest_name_underscore, f"{forest_name_underscore}_{time_period}_Tile_{tile_id}_NDVI.tif")

                # If still not found, try alternative location
                if not os.path.exists(ndvi_path):
                    alt_ndvi_dir = os.path.join(base_dir, "NDVI_Outputs_Improved")
                    alt_ndvi_path = os.path.join(alt_ndvi_dir, forest_name, f"{forest_name}_{time_period}_Tile_{tile_id}_NDVI.tif")
                    if os.path.exists(alt_ndvi_path):
                        ndvi_path = alt_ndvi_path

                ndvi_paths[time_period] = ndvi_path

                # If this is not the last time period, find ternary mask, distance map, and NDVI diff
                if i < len(all_time_periods) - 1:
                    next_time_period = all_time_periods[i + 1]

                    # Find ternary mask
                    ternary_mask_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period}_to_{next_time_period}_Tile_{tile_id}_Ternary_Mask.tif")

                    # If not found, try with underscores
                    if not os.path.exists(ternary_mask_path):
                        ternary_mask_path = os.path.join(ndvi_dir, forest_name_underscore, f"{forest_name_underscore}_{time_period}_to_{next_time_period}_Tile_{tile_id}_Ternary_Mask.tif")

                    # If still not found, try alternative location
                    if not os.path.exists(ternary_mask_path):
                        alt_ndvi_dir = os.path.join(base_dir, "NDVI_Outputs_Improved")
                        alt_ternary_mask_path = os.path.join(alt_ndvi_dir, forest_name, f"{forest_name}_{time_period}_to_{next_time_period}_Tile_{tile_id}_Ternary_Mask.tif")
                        if os.path.exists(alt_ternary_mask_path):
                            ternary_mask_path = alt_ternary_mask_path

                    # Find distance map and NDVI diff based on ternary mask path
                    if os.path.exists(ternary_mask_path):
                        distance_map_path = ternary_mask_path.replace("Ternary_Mask.tif", "Distance_Map.tif")
                        ndvi_diff_path = ternary_mask_path.replace("Ternary_Mask.tif", "NDVI_Diff.tif")
                    else:
                        # Create default paths
                        distance_map_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period}_to_{next_time_period}_Tile_{tile_id}_Distance_Map.tif")
                        ndvi_diff_path = os.path.join(ndvi_dir, forest_name, f"{forest_name}_{time_period}_to_{next_time_period}_Tile_{tile_id}_NDVI_Diff.tif")

                    # Store paths
                    ternary_mask_paths[f"{time_period}_to_{next_time_period}"] = ternary_mask_path
                    distance_map_paths[f"{time_period}_to_{next_time_period}"] = distance_map_path
                    ndvi_diff_paths[f"{time_period}_to_{next_time_period}"] = ndvi_diff_path

            # Check if we have at least some data for this tile
            # Count how many satellite images and NDVI images exist
            existing_satellite_count = sum(1 for path in satellite_paths.values() if os.path.exists(path))
            existing_ndvi_count = sum(1 for path in ndvi_paths.values() if os.path.exists(path))

            # Very lenient filtering - include if we have at least one satellite image or one NDVI image
            if existing_satellite_count == 0 and existing_ndvi_count == 0:
                print(f"Skipping {forest_name} tile {tile_id} - no satellite or NDVI images found")
                continue

            # If we get here, we have at least some data for this tile
            print(f"Including {forest_name} tile {tile_id} with {existing_satellite_count} satellite images and {existing_ndvi_count} NDVI images")

            # Create a row for this tile with all time periods
            tile_data = {
                'forest': forest_name,
                'tile_id': tile_id
            }

            # Add paths for all time periods
            for time_period in all_time_periods:
                tile_data[f'satellite_path_{time_period}'] = satellite_paths[time_period]
                tile_data[f'ndvi_path_{time_period}'] = ndvi_paths[time_period]

            # Add paths for transitions between time periods
            for i in range(len(all_time_periods) - 1):
                time_period = all_time_periods[i]
                next_time_period = all_time_periods[i + 1]
                transition_key = f"{time_period}_to_{next_time_period}"

                tile_data[f'ternary_mask_path_{transition_key}'] = ternary_mask_paths.get(transition_key)
                tile_data[f'distance_map_path_{transition_key}'] = distance_map_paths.get(transition_key)
                tile_data[f'ndvi_diff_path_{transition_key}'] = ndvi_diff_paths.get(transition_key)

            # Add to dataset
            data.append(tile_data)

    # No special handling for any specific forest tiles

    # Create DataFrame
    df = pd.DataFrame(data)

    # Store output path in dataframe attributes for later use
    df.attrs['output_csv_path'] = output_csv_path

    # Save to CSV
    df.to_csv(output_csv_path, index=False)

    print(f"Dataset created with {len(df)} samples")
    print(f"Saved to {output_csv_path}")

    return df

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Prepare dataset for deforestation prediction with sequences')
    parser.add_argument('--input_csv', type=str, default=None,
                        help='Path to input CSV file with NDVI data information')
    parser.add_argument('--base_dir', type=str, default=r"E:\Sentinelv3",
                        help='Base directory for all data (default: E:\Sentinelv3)')
    parser.add_argument('--ndvi_dir', type=str, default=None,
                        help='Directory containing NDVI outputs (default: base_dir/NDVI_Outputs_v2)')
    parser.add_argument('--output_csv', type=str, default=None,
                        help='Output CSV file path (default: base_dir/ModelReady/Deforestation_Prediction_Data_Sequence.csv)')
    args = parser.parse_args()

    # Create the prediction dataset
    df = create_prediction_dataset(
        output_csv_path=args.output_csv,
        input_csv=args.input_csv,
        ndvi_dir=args.ndvi_dir,
        base_dir=args.base_dir
    )

    # Print some statistics
    print("\nDataset Statistics:")
    if len(df) > 0:
        print(f"Number of forests: {df['forest'].nunique()}")
        print(f"Number of time periods: 5")  # We're using all 5 time periods
        print(f"Number of tiles: {df['tile_id'].nunique()}")

        # Count satellite images
        satellite_count = 0
        for col in df.columns:
            if 'satellite_path_' in col and df[col].notna().any():
                satellite_count += df[col].notna().sum()
        print(f"Total satellite images: {satellite_count}")
    else:
        print("Dataset is empty. No statistics available.")

    # Check for missing files and provide statistics
    if len(df) > 0:
        # Count missing files by type
        missing_files_by_type = {}
        total_files_by_type = {}

        # Get all time periods from column names
        time_periods = ["2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024"]

        # Check all file paths in each row
        for idx, row in df.iterrows():
            for key in row.keys():
                if 'path' in key and isinstance(row[key], str):
                    # Count total files by type
                    file_type = key.split('_')[0]  # Extract type (satellite, ndvi, etc.)
                    if file_type not in total_files_by_type:
                        total_files_by_type[file_type] = 0
                    total_files_by_type[file_type] += 1

                    # Check if file exists
                    if not os.path.exists(row[key]):
                        if file_type not in missing_files_by_type:
                            missing_files_by_type[file_type] = 0
                        missing_files_by_type[file_type] += 1

        # Print statistics
        print("\nFile Existence Statistics:")
        for file_type in total_files_by_type:
            missing = missing_files_by_type.get(file_type, 0)
            total = total_files_by_type[file_type]
            existing = total - missing
            print(f"{file_type.capitalize()} files: {existing}/{total} exist ({existing/total*100:.1f}%)")

        # Calculate overall statistics
        total_files = sum(total_files_by_type.values())
        total_missing = sum(missing_files_by_type.values())
        total_existing = total_files - total_missing
        print(f"\nOverall: {total_existing}/{total_files} files exist ({total_existing/total_files*100:.1f}%)")

        # Get the output CSV path from the dataframe creation
        output_csv_path = df.attrs.get('output_csv_path', 'dataset.csv')

        # Save the dataset as is - we're being lenient with missing files
        print(f"Saved dataset with {len(df)} samples to {output_csv_path}")

        # Create a filtered version with only rows that have at least one satellite image per time period
        filtered_rows = []
        for idx, row in df.iterrows():
            # Check if at least one satellite image exists for each time period
            has_satellite_images = False
            for time_period in time_periods:
                satellite_key = f'satellite_path_{time_period}'
                if satellite_key in row and os.path.exists(row[satellite_key]):
                    has_satellite_images = True
                    break

            if has_satellite_images:
                filtered_rows.append(idx)

        # Create filtered dataframe
        filtered_df = df.loc[filtered_rows]
        filtered_csv_path = os.path.join(os.path.dirname(output_csv_path), "Deforestation_Prediction_Data_Sequence_Filtered.csv")
        filtered_df.to_csv(filtered_csv_path, index=False)
        print(f"Saved filtered dataset with {len(filtered_df)} samples to {filtered_csv_path}")
    else:
        print("No files to check since dataset is empty.")
