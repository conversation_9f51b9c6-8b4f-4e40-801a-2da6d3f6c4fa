import os
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import torchvision.transforms as T
import numpy as np
from tqdm import tqdm
import random
from torch.amp import GradScaler, autocast
import matplotlib.pyplot as plt

# Import our custom dataset and model
from deforestation_prediction_dataset import DeforestationPredictionDataset, custom_prediction_collate
from improved_modelv3 import ImprovedDeforestationModelV3, CombinedLoss

# Set random seeds for reproducibility
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

# Training function for one epoch
def train_one_epoch(model, dataloader, optimizer, criterion, device, scaler=None):
    model.train()
    running_loss = 0.0
    running_seg_loss = 0.0
    running_reg_loss = 0.0

    # Use tqdm for progress bar
    pbar = tqdm(dataloader, desc="Training")

    for batch in pbar:
        # Get inputs and targets
        inputs = batch['input'].to(device)
        seg_targets = batch['target'].to(device).long()  # Shape: [B, H, W]
        reg_targets = batch['regression_target'].to(device).float()  # Shape: [B]

        # Zero the parameter gradients
        optimizer.zero_grad()

        # Forward pass with mixed precision
        if scaler is not None:
            with autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                # Forward pass
                outputs = model(
                    ndvi_input=inputs,
                    distance=batch['distance'].to(device) if 'distance' in batch else None,
                    ndvi_diff=batch['ndvi_diff'].to(device) if 'ndvi_diff' in batch else None,
                    apply_post_processing=False
                )

                # Get segmentation and regression outputs
                seg_output = outputs['segmentation']
                reg_output = outputs['regression'].squeeze()

                # Calculate loss
                loss, seg_loss, reg_loss = criterion(seg_output, seg_targets, reg_output, reg_targets)

            # Backward pass with gradient scaling
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            # Forward pass
            outputs = model(
                ndvi_input=inputs,
                distance=batch['distance'].to(device) if 'distance' in batch else None,
                ndvi_diff=batch['ndvi_diff'].to(device) if 'ndvi_diff' in batch else None,
                apply_post_processing=False
            )

            # Get segmentation and regression outputs
            seg_output = outputs['segmentation']
            reg_output = outputs['regression'].squeeze()

            # Calculate loss
            loss, seg_loss, reg_loss = criterion(seg_output, seg_targets, reg_output, reg_targets)

            # Backward pass
            loss.backward()
            optimizer.step()

        # Update running losses
        running_loss += loss.item()
        running_seg_loss += seg_loss.item()
        running_reg_loss += reg_loss.item()

        # Update progress bar
        pbar.set_postfix({
            'loss': running_loss / (pbar.n + 1),
            'seg_loss': running_seg_loss / (pbar.n + 1),
            'reg_loss': running_reg_loss / (pbar.n + 1)
        })

    # Calculate average losses
    epoch_loss = running_loss / len(dataloader)
    epoch_seg_loss = running_seg_loss / len(dataloader)
    epoch_reg_loss = running_reg_loss / len(dataloader)

    return epoch_loss, epoch_seg_loss, epoch_reg_loss

# Validation function
def validate(model, dataloader, criterion, device, config=None):
    model.eval()
    running_loss = 0.0
    running_seg_loss = 0.0
    running_reg_loss = 0.0

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Validating"):
            # Get inputs and targets
            inputs = batch['input'].to(device)
            seg_targets = batch['target'].to(device).long()  # Shape: [B, H, W]
            reg_targets = batch['regression_target'].to(device).float()  # Shape: [B]

            # Forward pass
            outputs = model(
                ndvi_input=inputs,
                distance=batch['distance'].to(device) if 'distance' in batch else None,
                ndvi_diff=batch['ndvi_diff'].to(device) if 'ndvi_diff' in batch else None,
                apply_post_processing=True if config and config.get('use_post_processing', False) else False
            )

            # Get segmentation and regression outputs
            seg_output = outputs['segmentation']
            reg_output = outputs['regression'].squeeze()

            # Calculate loss
            loss, seg_loss, reg_loss = criterion(seg_output, seg_targets, reg_output, reg_targets)

            # Update running losses
            running_loss += loss.item()
            running_seg_loss += seg_loss.item()
            running_reg_loss += reg_loss.item()

    # Calculate average losses
    epoch_loss = running_loss / len(dataloader)
    epoch_seg_loss = running_seg_loss / len(dataloader)
    epoch_reg_loss = running_reg_loss / len(dataloader)

    return epoch_loss, epoch_seg_loss, epoch_reg_loss

# Function to display training metrics
def display_training_metrics(history, output_dir):
    """Display and save training metrics as graphs.

    Args:
        history (dict): Dictionary containing training and validation metrics
        output_dir (str): Directory to save the plots
    """
    # Create figure with 2 subplots
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # Plot 1: Overall Loss
    axes[0].plot(history['train_loss'], label='Train Loss', marker='o')
    axes[0].plot(history['val_loss'], label='Validation Loss', marker='x')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True, linestyle='--', alpha=0.6)

    # Plot 2: Segmentation Loss
    axes[1].plot(history['train_seg_loss'], label='Train Seg Loss', marker='o')
    axes[1].plot(history['val_seg_loss'], label='Val Seg Loss', marker='x')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Segmentation Loss')
    axes[1].set_title('Segmentation Loss')
    axes[1].legend()
    axes[1].grid(True, linestyle='--', alpha=0.6)

    # Plot 3: Regression Loss
    axes[2].plot(history['train_reg_loss'], label='Train Reg Loss', marker='o')
    axes[2].plot(history['val_reg_loss'], label='Val Reg Loss', marker='x')
    axes[2].set_xlabel('Epoch')
    axes[2].set_ylabel('Regression Loss')
    axes[2].set_title('Regression Loss')
    axes[2].legend()
    axes[2].grid(True, linestyle='--', alpha=0.6)

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_metrics.png'), dpi=300)

    # Display the plot if running in an interactive environment
    try:
        plt.show()
    except:
        pass
    finally:
        plt.close()

    # Print final metrics
    print(f"\nFinal Training Metrics:")
    print(f"Train Loss: {history['train_loss'][-1]:.4f}, Val Loss: {history['val_loss'][-1]:.4f}")
    print(f"Train Seg Loss: {history['train_seg_loss'][-1]:.4f}, Val Seg Loss: {history['val_seg_loss'][-1]:.4f}")
    print(f"Train Reg Loss: {history['train_reg_loss'][-1]:.4f}, Val Reg Loss: {history['val_reg_loss'][-1]:.4f}")

# Early stopping class
class EarlyStopping:
    def __init__(self, patience=7, delta=0):
        self.patience = patience
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        self.val_loss_min = float('inf')
        self.delta = delta

    def __call__(self, val_loss, model, path):
        score = -val_loss

        if self.best_score is None:
            # First epoch
            self.best_score = score
            self.save_checkpoint(val_loss, model, path)
        elif score < self.best_score + self.delta:
            # Performance decreased
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            # Performance improved
            self.best_score = score
            self.save_checkpoint(val_loss, model, path)
            self.counter = 0

    def save_checkpoint(self, val_loss, model, path):
        torch.save({
            'model_state_dict': model.state_dict(),
            'val_loss': val_loss
        }, path)
        self.val_loss_min = val_loss

def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Train deforestation prediction model')
    parser.add_argument('--input_csv', type=str, default=r"E:\Sentinelv3\ModelReady\Deforestation_Prediction_Data.csv",
                        help='Path to input CSV file with prediction data')
    parser.add_argument('--output_dir', type=str, default=r"E:\Sentinelv3\Models\deforestation_prediction_model",
                        help='Directory to save the trained model')
    parser.add_argument('--batch_size', type=int, default=8,
                        help='Batch size for training')
    parser.add_argument('--epochs', type=int, default=50,
                        help='Number of epochs to train')
    parser.add_argument('--num_workers', type=int, default=4,
                        help='Number of worker threads for data loading')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for reproducibility')
    args = parser.parse_args()

    # Configuration
    config = {
        'csv_file': args.input_csv,
        'output_dir': args.output_dir,
        'batch_size': args.batch_size,
        'num_workers': args.num_workers,
        'learning_rate': 1e-4,
        'weight_decay': 1e-5,
        'num_epochs': args.epochs,
        'use_amp': True,
        'use_distance': True,
        'use_satellite': True,  # We're now using satellite imagery directly
        'use_augmentation': True,
        'use_post_processing': True,
        'val_split': 0.2,
        'seed': args.seed
    }

    # Set random seed
    set_seed(config['seed'])

    # Create output directory
    os.makedirs(config['output_dir'], exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Define transforms
    input_transform = T.Compose([
        T.Resize((224, 224)),
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    distance_transform = T.Compose([
        T.Resize((224, 224), interpolation=T.InterpolationMode.BILINEAR),
        T.ToTensor()
    ])

    # Create dataset
    print(f"Loading data from {config['csv_file']}")
    dataset = DeforestationPredictionDataset(
        csv_file=config['csv_file'],
        transform_input=input_transform,
        transform_target=None,  # Handle in __getitem__
        transform_distance=distance_transform,
        use_augmentation=config['use_augmentation'],
        use_satellite=config['use_satellite']
    )

    # Split dataset into train and validation sets
    val_size = int(config['val_split'] * len(dataset))
    train_size = len(dataset) - val_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=config['num_workers'],
        pin_memory=True,
        collate_fn=custom_prediction_collate
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True,
        collate_fn=custom_prediction_collate
    )

    print(f"Dataset size: {len(dataset)}")
    print(f"Training set size: {len(train_dataset)}")
    print(f"Validation set size: {len(val_dataset)}")

    # Print dataset information
    print(f"Dataset ready for training")

    # Create model
    model = ImprovedDeforestationModelV3(
        num_seg_classes=3,  # 3 classes: stable, deforestation, growth
        pretrained=True,  # Use pretrained weights
        use_distance=config['use_distance'],
        use_ndvi_diff=False,  # Not using NDVI diff for prediction
        dropout_rate=0.3
    ).to(device)

    # Define loss function with adjusted class weights
    # Based on class distribution: Deforestation (29.0%), Stable (70.0%), Growth (1.0%)
    # Further adjusted weights to find a better balance between classes
    # Increasing Deforestation weight, reducing Stable weight
    criterion = CombinedLoss(
        seg_weight=1.0,
        reg_weight=0.1,
        class_weights=torch.tensor([12.0, 8.0, 10.0]).to(device)  # Balanced weights to prevent Stable class dominance
    )

    # Define optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )

    # Define learning rate scheduler
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,  # Restart every 10 epochs
        T_mult=2,  # Double the restart period after each restart
        eta_min=config['learning_rate'] / 100  # Minimum learning rate
    )

    # Initialize gradient scaler for mixed precision training
    scaler = GradScaler() if config['use_amp'] and torch.cuda.is_available() else None

    # Initialize early stopping
    early_stopping = EarlyStopping(patience=10)
    early_stopping_path = os.path.join(config['output_dir'], 'early_stopping_model.pth')

    # Initialize best validation loss
    best_val_loss = float('inf')

    # Training history
    history = {
        'train_loss': [],
        'train_seg_loss': [],
        'train_reg_loss': [],
        'val_loss': [],
        'val_seg_loss': [],
        'val_reg_loss': []
    }

    # Training loop
    for epoch in range(config['num_epochs']):
        print(f"\nEpoch {epoch+1}/{config['num_epochs']}")

        # Train one epoch
        train_loss, train_seg_loss, train_reg_loss = train_one_epoch(
            model, train_loader, optimizer, criterion, device, scaler)

        # Validate
        val_loss, val_seg_loss, val_reg_loss = validate(
            model, val_loader, criterion, device, config)

        # Update learning rate
        scheduler.step()

        # Print epoch results
        print(f"Train Loss: {train_loss:.4f}, Seg Loss: {train_seg_loss:.4f}, Reg Loss: {train_reg_loss:.4f}")
        print(f"Val Loss: {val_loss:.4f}, Seg Loss: {val_seg_loss:.4f}, Reg Loss: {val_reg_loss:.4f}")

        # Update history
        history['train_loss'].append(train_loss)
        history['train_seg_loss'].append(train_seg_loss)
        history['train_reg_loss'].append(train_reg_loss)
        history['val_loss'].append(val_loss)
        history['val_seg_loss'].append(val_seg_loss)
        history['val_reg_loss'].append(val_reg_loss)

        # Save checkpoint if validation loss improved
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            checkpoint_path = os.path.join(config['output_dir'], f"best_model.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'history': history,
                'config': config
            }, checkpoint_path)
            print(f"Saved best model checkpoint to {checkpoint_path}")

        # Early stopping
        early_stopping(val_loss, model, early_stopping_path)
        if early_stopping.early_stop:
            print("Early stopping triggered. Training stopped.")
            break

        # Only saving best model and early stopping model

    # Display training metrics
    display_training_metrics(history, config['output_dir'])

    print(f"Training completed. Results saved to {config['output_dir']}")

if __name__ == "__main__":
    main()
