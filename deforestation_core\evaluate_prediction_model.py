import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, Subset
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
from tqdm import tqdm
import argparse
import pandas as pd

from deforestation_prediction_dataset import DeforestationPredictionDataset, custom_prediction_collate
from improved_modelv3 import ImprovedDeforestationModelV3

def evaluate_model(model_path, csv_file, output_dir="evaluation_output", num_samples=50):
    """
    Evaluate the deforestation prediction model on a test set.

    Args:
        model_path (str): Path to the trained model
        csv_file (str): Path to the CSV file with the dataset
        output_dir (str): Directory to save the evaluation results
        num_samples (int): Number of samples to evaluate
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Load model
    checkpoint = torch.load(model_path, map_location=device)

    # Get model configuration from checkpoint
    if isinstance(checkpoint, dict) and 'config' in checkpoint:
        config = checkpoint['config']
        use_distance = config.get('use_distance', True)
        use_satellite = config.get('use_satellite', True)
    else:
        # Default configuration
        use_distance = True
        use_satellite = True

    # Create model
    model = ImprovedDeforestationModelV3(
        num_seg_classes=3,  # 3 classes: stable, deforestation, growth
        pretrained=False,  # No need for pretrained weights when loading a trained model
        use_distance=use_distance,
        use_ndvi_diff=False,  # Not using NDVI diff for prediction
        dropout_rate=0.3
    ).to(device)

    # Load model weights
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()
    print(f"Model loaded from {model_path}")

    # Create dataset
    import torchvision.transforms as T

    input_transform = T.Compose([
        T.Resize((224, 224)),
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    distance_transform = T.Compose([
        T.Resize((224, 224), interpolation=T.InterpolationMode.BILINEAR),
        T.ToTensor()
    ])

    dataset = DeforestationPredictionDataset(
        csv_file=csv_file,
        transform_input=input_transform,
        transform_target=None,  # Handle in __getitem__
        transform_distance=distance_transform,
        use_augmentation=False,
        use_satellite=use_satellite
    )

    # Create a subset for evaluation
    indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    eval_dataset = Subset(dataset, indices)

    # Create data loader
    eval_loader = DataLoader(
        eval_dataset,
        batch_size=1,  # Process one sample at a time for visualization
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        collate_fn=custom_prediction_collate
    )

    # Initialize metrics
    all_preds = []
    all_targets = []
    all_file_paths = []

    # Evaluate model
    with torch.no_grad():
        for batch in tqdm(eval_loader, desc="Evaluating"):
            # Get inputs and targets
            inputs = batch['input'].to(device)
            targets = batch['target'].to(device).long()

            # Forward pass
            outputs = model(
                ndvi_input=inputs,
                distance=batch['distance'].to(device) if 'distance' in batch else None,
                ndvi_diff=batch['ndvi_diff'].to(device) if 'ndvi_diff' in batch else None,
                apply_post_processing=True
            )

            # Get segmentation output
            seg_output = outputs['segmentation']

            # Apply softmax to get probabilities
            probs = torch.softmax(seg_output, dim=1)

            # Get predicted class using argmax (standard approach)
            preds = torch.argmax(seg_output, dim=1)

            # Add to lists
            all_preds.append(preds.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
            all_file_paths.extend(batch['file_path'])

    # Flatten predictions and targets
    all_preds = np.concatenate([p.flatten() for p in all_preds])
    all_targets = np.concatenate([t.flatten() for t in all_targets])

    # Calculate metrics
    accuracy = accuracy_score(all_targets, all_preds)

    # Count occurrences of each class in targets and predictions
    target_counts = np.bincount(all_targets.astype(int), minlength=3)
    pred_counts = np.bincount(all_preds.astype(int), minlength=3)

    # Create confusion matrix
    cm = confusion_matrix(all_targets, all_preds, labels=[0, 1, 2])
    report = classification_report(all_targets, all_preds, labels=[0, 1, 2],
                                  target_names=['Deforestation', 'Stable', 'Growth'])

    # Print results
    print(f"\nAccuracy: {accuracy:.4f}")
    print("\nConfusion Matrix:")
    print(cm)
    print("\nClassification Report:")
    print(report)

    # Save results to file
    with open(os.path.join(output_dir, "evaluation_results.txt"), "w") as f:
        f.write(f"Model: {model_path}\n")
        f.write(f"Dataset: {csv_file}\n")
        f.write(f"Number of samples: {num_samples}\n\n")
        f.write(f"Accuracy: {accuracy:.4f}\n\n")
        f.write(f"Target class distribution: Deforestation (0): {target_counts[0]}, Stable (1): {target_counts[1]}, Growth (2): {target_counts[2]}\n")
        f.write(f"Prediction class distribution: Deforestation (0): {pred_counts[0]}, Stable (1): {pred_counts[1]}, Growth (2): {pred_counts[2]}\n\n")
        f.write("Confusion Matrix:\n")
        f.write(str(cm))
        f.write("\n\nClassification Report:\n")
        f.write(report)

    # Create confusion matrix visualization
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    plt.colorbar()

    classes = ['Deforestation', 'Stable', 'Growth']
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)

    # Add text annotations
    thresh = cm.max() / 2
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")

    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')

    plt.savefig(os.path.join(output_dir, "confusion_matrix.png"))
    plt.close()

    # Create a DataFrame with file paths and accuracy
    results_df = pd.DataFrame({
        'file_path': all_file_paths,
        'accuracy': [accuracy] * len(all_file_paths)
    })

    # Save to CSV
    results_df.to_csv(os.path.join(output_dir, "sample_accuracies.csv"), index=False)

    print(f"\nEvaluation results saved to {output_dir}")

    return accuracy, cm, report

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate deforestation prediction model")
    parser.add_argument("--model", required=True, help="Path to the trained model")
    parser.add_argument("--csv", default=r"E:\Sentinelv3\NDVI_Outputs_Prediction\Deforestation_Prediction_Data.csv",
                        help="Path to the CSV file with the dataset")
    parser.add_argument("--output", default="evaluation_output", help="Directory to save the evaluation results")
    parser.add_argument("--samples", type=int, default=50, help="Number of samples to evaluate")

    args = parser.parse_args()

    evaluate_model(
        model_path=args.model,
        csv_file=args.csv,
        output_dir=args.output,
        num_samples=args.samples
    )
