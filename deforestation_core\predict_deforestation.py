import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import rasterio
from PIL import Image
import torchvision.transforms as T
import cv2
from matplotlib.colors import LinearSegmentedColormap
from mpl_toolkits.axes_grid1 import make_axes_locatable
from improved_modelv3 import ImprovedDeforestationModelV3
from deforestation_prediction_dataset import calculate_ndvi_from_satellite

def predict_deforestation(satellite_path, model_path, output_dir="prediction_output", ndvi_path=None, next_satellite_path=None,
                     time_periods=None, satellite_paths=None, ndvi_paths=None):
    """
    Predict deforestation for a satellite image or sequence of images.

    Args:
        satellite_path (str): Path to the satellite image (for single image prediction)
        model_path (str): Path to the trained model
        output_dir (str): Directory to save the output
        ndvi_path (str, optional): Path to the NDVI image. If None, NDVI will be calculated from the satellite image.
        next_satellite_path (str, optional): Path to the next time period satellite image for comparison.
        time_periods (list, optional): List of time periods for sequence prediction.
        satellite_paths (dict, optional): Dictionary mapping time periods to satellite image paths.
        ndvi_paths (dict, optional): Dictionary mapping time periods to NDVI image paths.

    Returns:
        dict: Dictionary with prediction results
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Load model
    checkpoint = torch.load(model_path, map_location=device)

    # Get model configuration from checkpoint
    if isinstance(checkpoint, dict) and 'config' in checkpoint:
        config = checkpoint['config']
        use_distance = config.get('use_distance', True)
        use_satellite = config.get('use_satellite', True)
    else:
        # Default configuration
        use_distance = True
        use_satellite = True

    # Create model
    model = ImprovedDeforestationModelV3(
        num_seg_classes=3,  # 3 classes: deforestation, stable, growth
        pretrained=False,  # No need for pretrained weights when loading a trained model
        use_distance=use_distance,
        use_ndvi_diff=False,  # Not using NDVI diff for prediction
        dropout_rate=0.3
    ).to(device)

    # Load model weights
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()
    print(f"Model loaded from {model_path}")

    # Load satellite image
    if satellite_path and os.path.exists(satellite_path):
        print(f"Loading satellite image from {satellite_path}")
        with rasterio.open(satellite_path) as src:
            satellite_img = src.read([1, 2, 3])  # Read RGB bands
            satellite_img = np.transpose(satellite_img, (1, 2, 0))  # CHW to HWC

            # Normalize to 0-255 range for PIL
            satellite_img = (satellite_img / satellite_img.max() * 255).astype(np.uint8)
            satellite_pil = Image.fromarray(satellite_img)
    else:
        satellite_pil = None
        print("No satellite image provided or file not found")

    # Load next time period satellite image if available
    next_satellite_pil = None
    next_ndvi = None
    if next_satellite_path and os.path.exists(next_satellite_path):
        print(f"Loading next time period satellite image from {next_satellite_path}")
        try:
            with rasterio.open(next_satellite_path) as src:
                next_satellite_img = src.read([1, 2, 3])  # Read RGB bands
                next_satellite_img = np.transpose(next_satellite_img, (1, 2, 0))  # CHW to HWC

                # Normalize to 0-255 range for PIL
                next_satellite_img = (next_satellite_img / next_satellite_img.max() * 255).astype(np.uint8)
                next_satellite_pil = Image.fromarray(next_satellite_img)

                # Calculate NDVI for next time period
                next_ndvi = calculate_ndvi_from_satellite(next_satellite_path)
                print(f"Next time period NDVI calculated, shape: {next_ndvi.shape}")
        except Exception as e:
            print(f"Error loading next satellite image: {e}")

    # Load or calculate NDVI
    if ndvi_path and os.path.exists(ndvi_path):
        print(f"Loading NDVI image from {ndvi_path}")
        with rasterio.open(ndvi_path) as src:
            ndvi = src.read(1).astype(np.float32)

            # Normalize NDVI to 0-1 range (same as in dataset class)
            ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
            ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
            ndvi_norm = np.clip(ndvi_norm, 0, 1)

            # Convert to RGB image (for model input) - SAME AS TRAINING DATA
            ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

            # Green channel represents vegetation (high NDVI)
            ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

            # Red for very low NDVI (potential deforestation)
            ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

            # Blue can be constant or another representation
            ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)

            ndvi_pil = Image.fromarray(ndvi_rgb)
    elif satellite_path and os.path.exists(satellite_path):
        print("Calculating NDVI from satellite image")
        ndvi = calculate_ndvi_from_satellite(satellite_path)

        # Normalize NDVI to 0-1 range
        ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
        ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
        ndvi_norm = np.clip(ndvi_norm, 0, 1)

        # Convert to RGB image (for model input) - SAME AS TRAINING DATA
        ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

        # Green channel represents vegetation (high NDVI)
        ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

        # Red for very low NDVI (potential deforestation)
        ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

        # Blue can be constant or another representation
        ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)

        ndvi_pil = Image.fromarray(ndvi_rgb)
    else:
        print("No NDVI or satellite image provided")
        return None

    # Calculate NDVI difference if next time period satellite image is available
    ndvi_diff = None
    ground_truth_deforestation_percent = None
    ground_truth_stable_percent = None
    ground_truth_growth_percent = None

    if next_ndvi is not None and ndvi is not None:
        try:
            print("Calculating NDVI difference between time periods")
            # Ensure both arrays have the same shape
            if next_ndvi.shape == ndvi.shape:
                ndvi_diff = next_ndvi - ndvi
                print(f"NDVI difference range: min={ndvi_diff.min():.4f}, max={ndvi_diff.max():.4f}, mean={ndvi_diff.mean():.4f}")
            else:
                print(f"Warning: NDVI shapes don't match. Current: {ndvi.shape}, Next: {next_ndvi.shape}")
                # Convert to PIL images for resizing
                ndvi_pil_for_resize = Image.fromarray((ndvi * 255).astype(np.uint8))
                next_ndvi_pil_for_resize = Image.fromarray((next_ndvi * 255).astype(np.uint8))

                # Resize next_ndvi to match ndvi
                next_ndvi_resized_pil = next_ndvi_pil_for_resize.resize((ndvi.shape[1], ndvi.shape[0]), Image.BILINEAR)
                next_ndvi_resized = np.array(next_ndvi_resized_pil).astype(np.float32) / 255.0

                # Calculate difference
                ndvi_diff = next_ndvi_resized - ndvi

            # Calculate ground truth statistics from NDVI difference
            # Define thresholds for deforestation, stable, and growth
            deforestation_threshold = -0.1  # Negative difference indicates deforestation
            growth_threshold = 0.1  # Positive difference indicates growth

            # Create ground truth mask
            ground_truth = np.ones_like(ndvi_diff)  # Default is stable (1)
            ground_truth[ndvi_diff < deforestation_threshold] = 0  # Deforestation (0)
            ground_truth[ndvi_diff > growth_threshold] = 2  # Growth (2)

            # Calculate ground truth percentages
            total_pixels = ground_truth.size
            ground_truth_deforestation_percent = np.sum(ground_truth == 0) / total_pixels * 100
            ground_truth_stable_percent = np.sum(ground_truth == 1) / total_pixels * 100
            ground_truth_growth_percent = np.sum(ground_truth == 2) / total_pixels * 100

            print(f"Ground truth from NDVI difference:")
            print(f"Deforestation: {ground_truth_deforestation_percent:.2f}%")
            print(f"Stable: {ground_truth_stable_percent:.2f}%")
            print(f"Growth: {ground_truth_growth_percent:.2f}%")
        except Exception as e:
            print(f"Error calculating NDVI difference: {e}")

    # Prepare input for model - use the SAME transform as in the training dataset
    input_transform = T.Compose([
        T.Resize((224, 224)),
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])  # ImageNet normalization
    ])

    # Transform NDVI to tensor - now it's already in RGB format
    ndvi_tensor = input_transform(ndvi_pil)

    # Print tensor stats for debugging
    print(f"NDVI tensor shape: {ndvi_tensor.shape}")
    print(f"NDVI tensor min: {ndvi_tensor.min()}, max: {ndvi_tensor.max()}, mean: {ndvi_tensor.mean()}")
    print(f"NDVI tensor channel means: R={ndvi_tensor[0].mean()}, G={ndvi_tensor[1].mean()}, B={ndvi_tensor[2].mean()}")

    # The NDVI is already in RGB format, so we can use it directly
    rgb_like = ndvi_tensor.unsqueeze(0).to(device)

    # Create a zero tensor for the distance map
    distance_map = torch.zeros((1, 1, 224, 224), device=device)

    # Forward pass using the model's forward method directly
    with torch.no_grad():
        outputs = model.forward(
            ndvi_input=rgb_like,  # Use the 3-channel RGB-like input
            distance=distance_map,  # Provide the distance map
            ndvi_diff=None,  # Not using NDVI diff
            apply_post_processing=False  # Disable post-processing to see raw model output
        )

    # Get segmentation output
    seg_output = outputs['segmentation']

    # Print raw model output statistics
    print("\nRaw model output statistics:")
    print(f"Segmentation output shape: {seg_output.shape}")

    # Get softmax probabilities
    probs = torch.softmax(seg_output, dim=1).cpu().numpy()[0]
    print(f"Class 0 (Deforestation) probability: min={probs[0].min():.4f}, max={probs[0].max():.4f}, mean={probs[0].mean():.4f}")
    print(f"Class 1 (Stable) probability: min={probs[1].min():.4f}, max={probs[1].max():.4f}, mean={probs[1].mean():.4f}")
    print(f"Class 2 (Growth) probability: min={probs[2].min():.4f}, max={probs[2].max():.4f}, mean={probs[2].mean():.4f}")

    # Print raw logits
    logits = seg_output.cpu().numpy()[0]
    print(f"Class 0 (Deforestation) logits: min={logits[0].min():.4f}, max={logits[0].max():.4f}, mean={logits[0].mean():.4f}")
    print(f"Class 1 (Stable) logits: min={logits[1].min():.4f}, max={logits[1].max():.4f}, mean={logits[1].mean():.4f}")
    print(f"Class 2 (Growth) logits: min={logits[2].min():.4f}, max={logits[2].max():.4f}, mean={logits[2].mean():.4f}")

    # Get predicted class using argmax (standard approach)
    preds_standard = torch.argmax(seg_output, dim=1).cpu().numpy()[0]

    # Apply custom thresholding to get more balanced predictions
    # Convert logits to probabilities
    probs_tensor = torch.softmax(seg_output, dim=1)[0].cpu()

    # Apply class-specific thresholding to get more balanced predictions
    # Start with all pixels unassigned (-1)
    preds = -np.ones_like(preds_standard)

    # Convert to numpy for easier manipulation
    probs_np = probs_tensor.numpy()

    # Print detailed probability distribution statistics
    print("\nProbability distribution statistics:")
    print(f"Deforestation: min={probs_np[0].min():.4f}, 25th={np.percentile(probs_np[0], 25):.4f}, median={np.median(probs_np[0]):.4f}, 75th={np.percentile(probs_np[0], 75):.4f}, max={probs_np[0].max():.4f}, mean={probs_np[0].mean():.4f}")
    print(f"Stable: min={probs_np[1].min():.4f}, 25th={np.percentile(probs_np[1], 25):.4f}, median={np.median(probs_np[1]):.4f}, 75th={np.percentile(probs_np[1], 75):.4f}, max={probs_np[1].max():.4f}, mean={probs_np[1].mean():.4f}")
    print(f"Growth: min={probs_np[2].min():.4f}, 25th={np.percentile(probs_np[2], 25):.4f}, median={np.median(probs_np[2]):.4f}, 75th={np.percentile(probs_np[2], 75):.4f}, max={probs_np[2].max():.4f}, mean={probs_np[2].mean():.4f}")

    # Calculate adaptive thresholds based on the distribution of probabilities
    # Use percentiles to determine thresholds
    deforest_percentile = 70  # Lower percentile = more deforestation predictions
    stable_percentile = 30    # Lower percentile = more stable predictions
    growth_percentile = 90    # Lower percentile = more growth predictions

    # Calculate thresholds using percentiles
    deforest_threshold = np.percentile(probs_np[0], deforest_percentile)
    stable_threshold = np.percentile(probs_np[1], stable_percentile)
    growth_threshold = np.percentile(probs_np[2], growth_percentile)

    print(f"\nAdaptive thresholds (based on percentiles):")
    print(f"Deforestation threshold (p{deforest_percentile}): {deforest_threshold:.4f}")
    print(f"Stable threshold (p{stable_percentile}): {stable_threshold:.4f}")
    print(f"Growth threshold (p{growth_percentile}): {growth_threshold:.4f}")

    # Apply thresholds in order of priority
    # First check for deforestation (priority to increase deforestation class representation)
    deforest_mask = probs_np[0] > deforest_threshold
    preds[deforest_mask] = 0

    # Then check for growth areas
    growth_mask = probs_np[2] > growth_threshold
    # Only set growth where it's not already set to deforestation
    growth_mask = growth_mask & ~deforest_mask
    preds[growth_mask] = 2

    # Finally check for stable areas (with higher threshold to reduce dominance)
    stable_mask = probs_np[1] > stable_threshold
    # Only set stable where it's not already set to deforestation or growth
    stable_mask = stable_mask & ~deforest_mask & ~growth_mask
    preds[stable_mask] = 1

    # For any remaining unassigned pixels (-1), assign to the class with highest probability
    unassigned_mask = preds == -1
    if np.any(unassigned_mask):
        preds[unassigned_mask] = np.argmax(probs_np[:, unassigned_mask], axis=0)

    print(f"\nApplied adaptive thresholds: Deforestation > {deforest_threshold:.4f}, Stable > {stable_threshold:.4f}, Growth > {growth_threshold:.4f}")
    print(f"Standard prediction (argmax) class distribution: Deforestation={np.sum(preds_standard == 0) / preds_standard.size * 100:.2f}%, "
          f"Stable={np.sum(preds_standard == 1) / preds_standard.size * 100:.2f}%, "
          f"Growth={np.sum(preds_standard == 2) / preds_standard.size * 100:.2f}%")

    # Calculate class percentages for thresholded prediction
    deforestation_percent = np.sum(preds == 0) / preds.size * 100
    stable_percent = np.sum(preds == 1) / preds.size * 100
    growth_percent = np.sum(preds == 2) / preds.size * 100

    # Create RGB image from class probabilities
    # R: Deforestation, G: Growth, B: Stable
    prob_img = np.zeros((*probs.shape[1:], 3))
    prob_img[:, :, 0] = probs[0]  # Deforestation (red)
    prob_img[:, :, 1] = probs[2]  # Growth (green)
    prob_img[:, :, 2] = probs[1]  # Stable (blue)

    # Create visualization
    plt.figure(figsize=(20, 15))

    # Plot current time period satellite image
    if satellite_pil:
        plt.subplot(2, 2, 1)
        plt.title("Current Satellite Image", fontsize=14)
        plt.imshow(satellite_pil)
        plt.axis('off')

    # Plot next time period satellite image if available
    if next_satellite_pil:
        plt.subplot(2, 2, 2)
        plt.title("Next Satellite Image", fontsize=14)
        plt.imshow(next_satellite_pil)
        plt.axis('off')
    else:
        plt.subplot(2, 2, 2)
        plt.title("Next Satellite Image (Not Available)", fontsize=14)
        plt.axis('off')

    # Always plot NDVI difference if available
    if ndvi_diff is not None:
        plt.subplot(2, 2, 3)
        plt.title("NDVI Difference", fontsize=14)
        # Use a diverging colormap for NDVI difference
        plt.imshow(ndvi_diff, cmap='RdBu', vmin=-0.5, vmax=0.5)
        plt.colorbar(label='NDVI Difference')
        plt.axis('off')
    else:
        plt.subplot(2, 2, 3)
        plt.title("NDVI (Difference Not Available)", fontsize=14)
        plt.imshow(ndvi, cmap='RdYlGn', vmin=-1, vmax=1)
        plt.colorbar(label='NDVI')
        plt.axis('off')

    # Create prediction heatmap overlay on satellite image
    if satellite_pil:
        # Create a figure with a special layout to accommodate the colorbar
        # First, create the main subplot for the prediction overlay
        ax_overlay = plt.subplot(2, 2, 4)
        plt.title("Prediction Overlay", fontsize=14)

        # Resize satellite image to match prediction size
        satellite_np = np.array(satellite_pil.resize((preds.shape[1], preds.shape[0]), Image.BILINEAR))

        # Create a combined heatmap overlay
        # Apply the heatmap with alpha based on class probabilities

        # Create a custom colormap for the overlay
        # Red for deforestation, green for growth, blue for stable
        overlay = satellite_np.copy()

        # Create a combined heatmap
        # Red channel for deforestation
        deforest_heatmap = cv2.applyColorMap((probs[0] * 255).astype(np.uint8), cv2.COLORMAP_HOT)
        deforest_heatmap = cv2.cvtColor(deforest_heatmap, cv2.COLOR_BGR2RGB)

        # Green channel for growth
        growth_heatmap = cv2.applyColorMap((probs[2] * 255).astype(np.uint8), cv2.COLORMAP_SUMMER)
        growth_heatmap = cv2.cvtColor(growth_heatmap, cv2.COLOR_BGR2RGB)

        # Create alpha masks based on probability
        deforest_alpha = probs[0].reshape(probs[0].shape[0], probs[0].shape[1], 1) * 0.7
        growth_alpha = probs[2].reshape(probs[2].shape[0], probs[2].shape[1], 1) * 0.7

        # Apply deforestation overlay first
        overlay = overlay * (1 - deforest_alpha) + deforest_heatmap * deforest_alpha

        # Then apply growth overlay where deforestation is low
        growth_mask = (1 - deforest_alpha) * growth_alpha
        overlay = overlay * (1 - growth_mask) + growth_heatmap * growth_mask

        overlay = overlay.astype(np.uint8)

        # Display the overlay
        plt.imshow(overlay)
        plt.axis('off')

        # Create a separate colorbar for the deforestation probability
        # This will be placed to the right of the prediction overlay
        divider = make_axes_locatable(ax_overlay)
        cax = divider.append_axes("right", size="5%", pad=0.1)

        # Create the colorbar
        deforest_cmap = plt.cm.get_cmap('hot')
        norm = plt.Normalize(vmin=0, vmax=1)
        cb = plt.colorbar(plt.cm.ScalarMappable(norm=norm, cmap=deforest_cmap), cax=cax)
        cb.set_label('Deforestation\nProbability', fontsize=10, fontweight='bold')

    # Add text with percentages - both prediction and ground truth
    if ground_truth_deforestation_percent is not None:
        plt.figtext(0.5, 0.03, f"Prediction: Deforestation: {deforestation_percent:.2f}% | Stable: {stable_percent:.2f}% | Growth: {growth_percent:.2f}%",
                    ha="center", fontsize=14, bbox={"facecolor":"white", "alpha":0.7, "pad":5})
        plt.figtext(0.5, 0.01, f"Ground Truth: Deforestation: {ground_truth_deforestation_percent:.2f}% | Stable: {ground_truth_stable_percent:.2f}% | Growth: {ground_truth_growth_percent:.2f}%",
                    ha="center", fontsize=14, bbox={"facecolor":"lightblue", "alpha":0.7, "pad":5})
    else:
        plt.figtext(0.5, 0.01, f"Prediction: Deforestation: {deforestation_percent:.2f}% | Stable: {stable_percent:.2f}% | Growth: {growth_percent:.2f}%",
                    ha="center", fontsize=14, bbox={"facecolor":"white", "alpha":0.7, "pad":5})

    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "prediction_visualization.png"), dpi=300)
    plt.close()

    # Create a histogram of probability distributions
    plt.figure(figsize=(12, 8))

    # Flatten the probability arrays for histograms
    deforest_probs = probs_np[0].flatten()
    stable_probs = probs_np[1].flatten()
    growth_probs = probs_np[2].flatten()

    # Plot histograms
    plt.hist(deforest_probs, bins=50, alpha=0.7, label='Deforestation', color='red')
    plt.hist(stable_probs, bins=50, alpha=0.7, label='Stable', color='blue')
    plt.hist(growth_probs, bins=50, alpha=0.7, label='Growth', color='green')

    # Add vertical lines for thresholds
    plt.axvline(x=deforest_threshold, color='red', linestyle='--', label=f'Deforestation Threshold: {deforest_threshold:.4f}')
    plt.axvline(x=stable_threshold, color='blue', linestyle='--', label=f'Stable Threshold: {stable_threshold:.4f}')
    plt.axvline(x=growth_threshold, color='green', linestyle='--', label=f'Growth Threshold: {growth_threshold:.4f}')

    # Add labels and title
    plt.xlabel('Probability')
    plt.ylabel('Frequency')
    plt.title('Probability Distribution Histograms with Adaptive Thresholds')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Save histogram
    plt.savefig(os.path.join(output_dir, "probability_histograms.png"), dpi=300)
    plt.close()

    # Create a separate overlay image that combines satellite with prediction
    if satellite_pil:
        # Resize satellite image to match prediction size if needed
        satellite_np = np.array(satellite_pil.resize((preds.shape[1], preds.shape[0]), Image.BILINEAR))

        # Create a colored mask for the prediction
        mask = np.zeros((preds.shape[0], preds.shape[1], 4), dtype=np.uint8)  # RGBA
        mask[preds == 0, :] = [255, 0, 0, 180]     # Red with alpha for deforestation
        mask[preds == 1, :] = [255, 255, 255, 100]  # White with alpha for stable
        mask[preds == 2, :] = [0, 255, 0, 180]     # Green with alpha for growth

        # Convert to PIL images for alpha compositing
        satellite_pil_resized = Image.fromarray(satellite_np)
        mask_pil = Image.fromarray(mask)

        # Composite the images
        overlay = Image.alpha_composite(satellite_pil_resized.convert('RGBA'), mask_pil)

        # Save the overlay
        overlay_path = os.path.join(output_dir, "prediction_overlay.png")
        overlay.save(overlay_path)
        print(f"Overlay saved to {overlay_path}")

    # Save prediction as GeoTIFF if satellite image is available
    if satellite_path and os.path.exists(satellite_path):
        with rasterio.open(satellite_path) as src:
            meta = src.meta.copy()
            meta.update(count=1, dtype='uint8')

            with rasterio.open(os.path.join(output_dir, "prediction.tif"), 'w', **meta) as dst:
                dst.write(preds.astype('uint8'), 1)

    # Return results
    results = {
        'deforestation_percent': deforestation_percent,
        'stable_percent': stable_percent,
        'growth_percent': growth_percent,
        'prediction': preds,
        'probabilities': probs
    }

    # Add ground truth results if available
    if ground_truth_deforestation_percent is not None:
        results.update({
            'ground_truth_deforestation_percent': ground_truth_deforestation_percent,
            'ground_truth_stable_percent': ground_truth_stable_percent,
            'ground_truth_growth_percent': ground_truth_growth_percent
        })

    return results

def calculate_ndvi_from_satellite(satellite_path):
    """
    Calculate NDVI from a satellite image.

    Args:
        satellite_path (str): Path to the satellite image

    Returns:
        numpy.ndarray: NDVI array
    """
    with rasterio.open(satellite_path) as src:
        # Read red and NIR bands
        red = src.read(1).astype('float32')
        nir = src.read(4).astype('float32')

        # Calculate NDVI
        np.seterr(divide='ignore', invalid='ignore')
        ndvi = np.where(
            (nir + red) > 0,
            (nir - red) / (nir + red),
            0
        )

        # Clip to valid range
        ndvi = np.clip(ndvi, -1.0, 1.0)

        return ndvi

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Predict deforestation for a satellite image or sequence")
    parser.add_argument("--satellite", default=None, help="Path to the current time period satellite image")
    parser.add_argument("--next-satellite", default=None, help="Path to the next time period satellite image (optional)")
    parser.add_argument("--model", required=True, help="Path to the trained model")
    parser.add_argument("--output", default=r"E:\Sentinelv3\PredictionOutput", help="Directory to save the output")
    parser.add_argument("--ndvi", default=None, help="Path to the NDVI image (optional)")
    parser.add_argument("--sequence-csv", default=None, help="Path to CSV file with sequence data (optional)")
    parser.add_argument("--tile-id", default=None, help="Tile ID to predict (when using sequence-csv)")
    parser.add_argument("--forest", default=None, help="Forest name to predict (when using sequence-csv)")

    args = parser.parse_args()

    # Check if we're using sequence data
    if args.sequence_csv and (args.tile_id or args.forest):
        import pandas as pd

        # Load sequence data
        df = pd.read_csv(args.sequence_csv)

        # Filter by tile_id and/or forest
        if args.tile_id and args.forest:
            filtered_df = df[(df['tile_id'].astype(str) == str(args.tile_id)) & (df['forest'] == args.forest)]
        elif args.tile_id:
            filtered_df = df[df['tile_id'].astype(str) == str(args.tile_id)]
        elif args.forest:
            filtered_df = df[df['forest'] == args.forest]

        if len(filtered_df) == 0:
            print(f"No matching data found for tile_id={args.tile_id}, forest={args.forest}")
            exit(1)

        # Use the first matching row
        row = filtered_df.iloc[0]

        # Get time periods from column names
        time_periods = []
        satellite_paths = {}
        ndvi_paths = {}

        for col in row.keys():
            if col.startswith('satellite_path_'):
                time_period = col.replace('satellite_path_', '')
                time_periods.append(time_period)
                satellite_paths[time_period] = row[col]

                # Get corresponding NDVI path
                ndvi_col = f'ndvi_path_{time_period}'
                if ndvi_col in row:
                    ndvi_paths[time_period] = row[ndvi_col]

        # Sort time periods
        time_periods.sort()

        # Use the first time period as the main satellite path
        satellite_path = satellite_paths[time_periods[0]] if time_periods else None

        # Use the second time period as the next satellite path
        next_satellite_path = satellite_paths[time_periods[1]] if len(time_periods) > 1 else None

        # Use the first time period's NDVI path
        ndvi_path = ndvi_paths[time_periods[0]] if time_periods and time_periods[0] in ndvi_paths else None

        print(f"Using sequence data for {args.forest} tile {args.tile_id}")
        print(f"Time periods: {time_periods}")
        print(f"Main satellite path: {satellite_path}")
        print(f"Next satellite path: {next_satellite_path}")
        print(f"NDVI path: {ndvi_path}")

        # Predict deforestation
        results = predict_deforestation(
            satellite_path=satellite_path,
            model_path=args.model,
            output_dir=args.output,
            ndvi_path=ndvi_path,
            next_satellite_path=next_satellite_path,
            time_periods=time_periods,
            satellite_paths=satellite_paths,
            ndvi_paths=ndvi_paths
        )
    else:
        # Use single image prediction
        if not args.satellite:
            print("Error: --satellite is required for single image prediction")
            exit(1)

        # Predict deforestation
        results = predict_deforestation(
            satellite_path=args.satellite,
            model_path=args.model,
            output_dir=args.output,
            ndvi_path=args.ndvi,
            next_satellite_path=args.next_satellite
        )

    if results:
        print("\nPrediction Results:")
        print(f"Deforestation: {results['deforestation_percent']:.2f}%")
        print(f"Stable: {results['stable_percent']:.2f}%")
        print(f"Growth: {results['growth_percent']:.2f}%")

        if 'ground_truth_deforestation_percent' in results:
            print("\nGround Truth Results (from NDVI difference):")
            print(f"Deforestation: {results['ground_truth_deforestation_percent']:.2f}%")
            print(f"Stable: {results['ground_truth_stable_percent']:.2f}%")
            print(f"Growth: {results['ground_truth_growth_percent']:.2f}%")

        print(f"\nResults saved to {args.output}")
