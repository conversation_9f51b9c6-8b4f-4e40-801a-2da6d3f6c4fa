2025-04-21 08:18:11,690 - INFO - Output directory created: E:\Sentinelv3\NDVI_Outputs_Improved
2025-04-21 08:18:11,712 - INFO - Loaded 5000 processed tiles from E:\Sentinelv3\Combined_Forest_FilePaths.csv
2025-04-21 08:18:11,713 - INFO - Using filtering parameters to remove problematic data:
2025-04-21 08:18:11,713 - INFO -   Cloud threshold: 0.3 (tiles with higher cloud coverage will be filtered out)
2025-04-21 08:18:11,714 - INFO -   Minimum valid pixels: 0.7 (tiles with fewer valid pixels will be filtered out)
2025-04-21 08:18:11,714 - INFO -   NDVI difference range: [-0.8, 0.8] (tiles with more extreme differences will be filtered out)
2025-04-21 08:18:11,714 - INFO -   Maximum extreme percentage: 0.2 (tiles with more extreme pixels will be filtered out)
2025-04-21 08:18:11,714 - INFO -   Minimum region size: 5 pixels (tiles with smaller deforestation/growth regions will be filtered out)
2025-04-21 08:18:11,714 - INFO -   Minimum coherence: 0.3 (tiles with less coherent deforestation/growth will be filtered out)
2025-04-21 08:18:11,714 - INFO -   Parallel processing: True
2025-04-21 08:18:11,716 - INFO -   Maximum workers: 4
2025-04-21 08:18:11,716 - INFO - STEP 1: Generating individual NDVI files for all satellite images...
2025-04-21 08:18:47,057 - INFO - Individual NDVI file index saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Individual_NDVI_Files.csv (5000 files)
2025-04-21 08:18:47,058 - INFO - STEP 2: Generating NDVI differences, ternary masks, and distance maps...
2025-04-21 08:18:47,058 - INFO - Processing 4 time period pairs
2025-04-21 08:18:47,058 - INFO - Processing time period pair: 2015_2016 to 2017_2018
2025-04-21 08:18:47,072 - INFO - Processing 1000 tile pairs for 2015_2016 to 2017_2018
2025-04-21 08:19:00,134 - INFO - Deforestation data saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Deforestation_Data_2015_2016_to_2017_2018.csv (384 valid tile pairs)
2025-04-21 08:19:00,138 - INFO - Added 384 valid tile pairs for 2015_2016 to 2017_2018
2025-04-21 08:19:00,138 - INFO - Processing time period pair: 2017_2018 to 2019_2020
2025-04-21 08:19:00,148 - INFO - Processing 1000 tile pairs for 2017_2018 to 2019_2020
2025-04-21 08:19:12,307 - INFO - Deforestation data saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Deforestation_Data_2017_2018_to_2019_2020.csv (394 valid tile pairs)
2025-04-21 08:19:12,312 - INFO - Added 394 valid tile pairs for 2017_2018 to 2019_2020
2025-04-21 08:19:12,312 - INFO - Processing time period pair: 2019_2020 to 2021_2022
2025-04-21 08:19:12,324 - INFO - Processing 1000 tile pairs for 2019_2020 to 2021_2022
2025-04-21 08:19:21,538 - INFO - Deforestation data saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Deforestation_Data_2019_2020_to_2021_2022.csv (219 valid tile pairs)
2025-04-21 08:19:21,542 - INFO - Added 219 valid tile pairs for 2019_2020 to 2021_2022
2025-04-21 08:19:21,542 - INFO - Processing time period pair: 2021_2022 to 2023_2024
2025-04-21 08:19:21,556 - INFO - Processing 1000 tile pairs for 2021_2022 to 2023_2024
2025-04-21 08:19:29,817 - INFO - Deforestation data saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Deforestation_Data_2021_2022_to_2023_2024.csv (141 valid tile pairs)
2025-04-21 08:19:29,820 - INFO - Added 141 valid tile pairs for 2021_2022 to 2023_2024
2025-04-21 08:19:29,846 - INFO - All deforestation pair data saved to: E:\Sentinelv3\NDVI_Outputs_Improved\Deforestation_Data_All_Pairs.csv (1138 total tile pairs)
2025-04-21 08:19:29,848 - INFO - NDVI data generation completed. Results saved to E:\Sentinelv3\NDVI_Outputs_Improved
2025-04-21 08:19:29,848 - INFO - Run 'python deforestation_core/prepare_prediction_dataset.py' to update the prediction dataset
