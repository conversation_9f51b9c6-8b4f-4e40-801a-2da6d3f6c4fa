import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import efficientnet_b3, EfficientNet_B3_Weights
import numpy as np
import cv2

# Feature Pyramid Network for multi-scale feature extraction
class FeaturePyramidNetwork(nn.Module):
    def __init__(self, in_channels_list, out_channels):
        super(FeaturePyramidNetwork, self).__init__()
        self.lateral_convs = nn.ModuleList()
        self.output_convs = nn.ModuleList()

        for in_channels in in_channels_list:
            self.lateral_convs.append(
                nn.Conv2d(in_channels, out_channels, kernel_size=1)
            )
            self.output_convs.append(
                nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
            )

        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, features):
        results = []
        # Last feature map (highest level)
        prev_features = self.lateral_convs[-1](features[-1])
        results.append(self.output_convs[-1](prev_features))

        # Process remaining feature maps from top to bottom
        for i in range(len(features) - 2, -1, -1):
            lateral_features = self.lateral_convs[i](features[i])
            # Upsample previous features
            top_down_features = F.interpolate(prev_features, size=lateral_features.shape[-2:],
                                             mode='bilinear', align_corners=False)
            # Add lateral features
            prev_features = lateral_features + top_down_features
            results.insert(0, self.output_convs[i](prev_features))

        return results

# Simplified Segmentation Head
class SegmentationHead(nn.Module):
    def __init__(self, in_channels, num_classes, dropout_rate=0.1):
        super(SegmentationHead, self).__init__()

        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_rate)
        )

        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels // 2, in_channels // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_rate)
        )

        self.final_conv = nn.Conv2d(in_channels // 4, num_classes, kernel_size=1)

    def forward(self, x):
        x = torch.nan_to_num(x)  # Handle NaN values
        x = self.conv1(x)
        x = self.conv2(x)
        return self.final_conv(x)

# Simplified Regression Head
class RegressionHead(nn.Module):
    def __init__(self, in_channels, dropout_rate=0.3):
        super(RegressionHead, self).__init__()

        # Global pooling
        self.gap = nn.AdaptiveAvgPool2d(1)
        self.gmp = nn.AdaptiveMaxPool2d(1)

        # Regression layers
        self.regression = nn.Sequential(
            nn.Linear(in_channels * 2, in_channels),
            nn.LayerNorm(in_channels),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(in_channels, 256),
            nn.LayerNorm(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(256, 64),
            nn.LayerNorm(64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(64, 1)
        )

    def forward(self, x):
        # Global average pooling
        gap_features = self.gap(x).squeeze(-1).squeeze(-1)  # [B, C]

        # Global max pooling
        gmp_features = self.gmp(x).squeeze(-1).squeeze(-1)  # [B, C]

        # Concatenate pooled features
        fused_features = torch.cat([gap_features, gmp_features], dim=1)  # [B, C*2]

        # Regression
        output = self.regression(fused_features)  # [B, 1]

        return output

# Improved Deforestation Model V3
class ImprovedDeforestationModelV3(nn.Module):
    def __init__(self, num_seg_classes=3, pretrained=True, use_distance=True,
                 use_ndvi_diff=False, dropout_rate=0.2):
        """
        Improved deforestation detection model V3 - streamlined version.

        Args:
            num_seg_classes (int): Number of segmentation classes.
            pretrained (bool): If True, load ImageNet pretrained weights.
            use_distance (bool): Whether to use distance map as input.
            use_ndvi_diff (bool): Whether to use NDVI difference as input.
            dropout_rate (float): Dropout rate for uncertainty estimation.
        """
        super(ImprovedDeforestationModelV3, self).__init__()
        self.use_distance = use_distance
        self.use_ndvi_diff = use_ndvi_diff

        # Calculate input channels
        base_channels = 3  # RGB image or NDVI converted to RGB
        if use_distance:
            base_channels += 1  # Distance map
        if use_ndvi_diff:
            base_channels += 1  # NDVI difference

        # 1. Backbone: EfficientNet-B3
        weights = EfficientNet_B3_Weights.DEFAULT if pretrained else None
        self.efficientnet = efficientnet_b3(weights=weights)

        # Modify first convolutional layer to accept our input channels
        if base_channels != 3:
            old_conv = self.efficientnet.features[0][0]
            new_conv = nn.Conv2d(
                base_channels,
                old_conv.out_channels,
                kernel_size=old_conv.kernel_size,
                stride=old_conv.stride,
                padding=old_conv.padding,
                bias=False
            )
            # Initialize new_conv weights
            with torch.no_grad():
                new_conv.weight[:, :3] = old_conv.weight
                if base_channels > 3:
                    for i in range(3, base_channels):
                        new_conv.weight[:, i] = old_conv.weight[:, 0].clone()

            self.efficientnet.features[0][0] = new_conv

        # Remove classifier
        self.efficientnet.classifier = nn.Identity()

        # Feature dimensions from different stages of EfficientNet
        self.feature_channels = [32, 48, 136]  # Based on EfficientNet-B3

        # Feature Pyramid Network
        self.fpn = FeaturePyramidNetwork(self.feature_channels, 256)

        # Decoder stages
        self.decoder_stages = nn.ModuleList()

        # Create decoder stages (from deep to shallow)
        decoder_in_channels = [512, 384, 160]  # Based on FPN output + previous stage output
        decoder_out_channels = [128, 64, 32]

        for i in range(len(decoder_in_channels)):
            # Decoder block
            self.decoder_stages.append(
                nn.Sequential(
                    nn.Conv2d(decoder_in_channels[i], decoder_out_channels[i], kernel_size=3, padding=1),
                    nn.BatchNorm2d(decoder_out_channels[i]),
                    nn.ReLU(inplace=True),
                    nn.Dropout2d(dropout_rate),
                    nn.Conv2d(decoder_out_channels[i], decoder_out_channels[i], kernel_size=3, padding=1),
                    nn.BatchNorm2d(decoder_out_channels[i]),
                    nn.ReLU(inplace=True)
                )
            )

        # Segmentation head
        self.seg_output = SegmentationHead(
            decoder_out_channels[-1], num_seg_classes, dropout_rate
        )

        # Regression head
        self.reg_head = RegressionHead(
            in_channels=self.feature_channels[-1],
            dropout_rate=dropout_rate
        )

    def extract_features(self, x):
        """Extract features from different stages of EfficientNet"""
        features = []

        # Extract features from different stages
        for i, block in enumerate(self.efficientnet.features):
            x = block(x)
            if i in [2, 3, 5]:  # Specific stages of EfficientNet
                features.append(x)

        return features

    def forward(self, ndvi_input, distance=None, ndvi_diff=None, apply_post_processing=False):
        """
        Forward pass through the improved model.

        Args:
            ndvi_input (Tensor): Tensor of shape [N, 3, H, W] - the NDVI image or satellite image.
            distance (Tensor): Tensor of shape [N, 1, H, W] - the distance map.
            ndvi_diff (Tensor): Tensor of shape [N, 1, H, W] - NDVI difference map.
            apply_post_processing (bool): Whether to apply post-processing to the segmentation output.

        Returns:
            dict: Dictionary containing segmentation and regression outputs.
        """
        # Create zero tensors for missing inputs
        batch_size, _, height, width = ndvi_input.shape
        device = ndvi_input.device

        # Handle missing inputs
        if distance is None and self.use_distance:
            distance = torch.zeros((batch_size, 1, height, width), device=device)

        if ndvi_diff is None and self.use_ndvi_diff:
            ndvi_diff = torch.zeros((batch_size, 1, height, width), device=device)

        # Prepare input by concatenating available features
        inputs = [ndvi_input]

        if self.use_distance:
            inputs.append(distance)

        if self.use_ndvi_diff:
            inputs.append(ndvi_diff)

        x = torch.cat(inputs, dim=1)

        # Extract features from backbone
        features = self.extract_features(x)

        # Apply FPN to get multi-scale features
        fpn_features = self.fpn(features)

        # Decoder with skip connections
        # Start with the deepest feature
        x = fpn_features[-1]  # Shape: [B, 256, 14, 14]

        # First upsampling and decoder stage
        x = F.interpolate(x, scale_factor=2, mode='bilinear', align_corners=False)
        x = torch.cat([x, fpn_features[1]], dim=1)
        x = self.decoder_stages[0](x)

        # Second upsampling and decoder stage
        x = F.interpolate(x, scale_factor=2, mode='bilinear', align_corners=False)
        x = torch.cat([x, fpn_features[0]], dim=1)
        x = self.decoder_stages[1](x)

        # Final upsampling and decoder stage
        x = F.interpolate(x, scale_factor=4, mode='bilinear', align_corners=False)
        x = nn.Conv2d(64, 160, kernel_size=1).to(x.device)(x)
        x = self.decoder_stages[2](x)

        # Segmentation output
        seg_output = self.seg_output(x)

        # Apply post-processing if requested
        if apply_post_processing and ndvi_diff is not None:
            seg_output = self.apply_post_processing(seg_output, ndvi_diff)

        # Regression output
        reg_features = features[-1]
        reg_output = self.reg_head(reg_features)

        # Return outputs as a dictionary
        return {
            'segmentation': seg_output,
            'regression': reg_output
        }

    def apply_post_processing(self, seg_output, ndvi_diff):
        """
        Apply post-processing to the segmentation output to improve quality.

        Args:
            seg_output (Tensor): Raw segmentation output from the model [N, C, H, W]
            ndvi_diff (Tensor): NDVI difference map to guide post-processing

        Returns:
            Tensor: Processed segmentation output
        """
        processed_output = seg_output.clone()
        batch_size = seg_output.shape[0]

        for b in range(batch_size):
            # Convert to probabilities
            probs = F.softmax(seg_output[b], dim=0)

            # Get class predictions
            pred_classes = torch.argmax(probs, dim=0)

            # Convert to numpy for OpenCV operations
            pred_np = pred_classes.detach().cpu().numpy().astype(np.uint8)

            # Create masks for each class
            deforestation_mask = (pred_np == 0).astype(np.uint8)
            growth_mask = (pred_np == 2).astype(np.uint8)

            # Define kernels for morphological operations
            kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

            # Apply morphological operations to remove noise
            deforestation_mask = cv2.morphologyEx(deforestation_mask, cv2.MORPH_OPEN, kernel_small)
            growth_mask = cv2.morphologyEx(growth_mask, cv2.MORPH_OPEN, kernel_small)

            # Use NDVI difference to refine predictions
            ndvi_diff_np = ndvi_diff[b, 0].detach().cpu().numpy()

            # Refine based on NDVI difference
            strong_deforestation = (ndvi_diff_np < -0.2)
            strong_growth = (ndvi_diff_np > 0.2)

            # Combine with morphological results
            deforestation_mask = np.logical_or(deforestation_mask,
                                             np.logical_and(strong_deforestation, ~growth_mask)).astype(np.uint8)
            growth_mask = np.logical_or(growth_mask,
                                      np.logical_and(strong_growth, ~deforestation_mask)).astype(np.uint8)

            # Create the final refined mask
            refined_mask = np.ones_like(pred_np)  # Default to stable (1)
            refined_mask[deforestation_mask == 1] = 0  # Deforestation class
            refined_mask[growth_mask == 1] = 2  # Growth class

            # Convert back to tensor
            refined_tensor = torch.from_numpy(refined_mask).to(seg_output.device).long()

            # Create one-hot encoding
            refined_one_hot = F.one_hot(refined_tensor, num_classes=3).permute(2, 0, 1).float()

            # Further balanced post-processing to prevent class bias
            # Use class-specific multipliers to better balance the predictions
            # Class 0 (Deforestation): Increased multiplier to boost representation
            # Class 1 (Stable): Reduced multiplier to prevent dominance
            # Class 2 (Growth): Moderate multiplier
            class_multipliers = [2.5, 1.5, 1.5]  # [Deforestation, Stable, Growth]
            for c in range(3):
                processed_output[b, c] = processed_output[b, c] * 0.7 + class_multipliers[c] * refined_one_hot[c]

        return processed_output

    def enable_dropout(self):
        """Enable dropout during inference for Monte Carlo dropout"""
        for m in self.modules():
            if isinstance(m, nn.Dropout) or isinstance(m, nn.Dropout2d):
                m.train()

    def monte_carlo_inference(self, ndvi_input, distance=None, ndvi_diff=None, n_samples=10):
        """
        Perform Monte Carlo dropout inference for uncertainty estimation.

        Args:
            ndvi_input (Tensor): Input tensor.
            distance (Tensor): Distance map tensor.
            ndvi_diff (Tensor): NDVI difference tensor.
            n_samples (int): Number of Monte Carlo samples.

        Returns:
            tuple: Mean and standard deviation of segmentation and regression predictions.
        """
        self.eval()
        self.enable_dropout()

        seg_outputs = []
        reg_outputs = []

        with torch.no_grad():
            for _ in range(n_samples):
                outputs = self.forward(ndvi_input, distance, ndvi_diff)
                seg_probs = F.softmax(outputs['segmentation'], dim=1)
                seg_outputs.append(seg_probs)
                reg_outputs.append(outputs['regression'])

        # Stack outputs
        seg_outputs = torch.stack(seg_outputs, dim=0)  # [S, B, C, H, W]
        reg_outputs = torch.stack(reg_outputs, dim=0)  # [S, B, 1]

        # Calculate mean and standard deviation
        seg_mean = torch.mean(seg_outputs, dim=0)  # [B, C, H, W]
        seg_std = torch.std(seg_outputs, dim=0)    # [B, C, H, W]
        reg_mean = torch.mean(reg_outputs, dim=0)  # [B, 1]
        reg_std = torch.std(reg_outputs, dim=0)    # [B, 1]

        return seg_mean, seg_std, reg_mean, reg_std

# Focal Loss for better handling of class imbalance
class FocalLoss(nn.Module):
    def __init__(self, alpha=None, gamma=2.0, reduction='mean', epsilon=1e-6):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha  # weight for each class
        self.reduction = reduction
        self.epsilon = epsilon  # small constant to prevent numerical instability
        self.ce_loss = nn.CrossEntropyLoss(weight=alpha, reduction='none')

    def forward(self, inputs, targets):
        # Handle NaN values
        inputs = torch.nan_to_num(inputs)

        # Standard cross entropy loss
        ce_loss = self.ce_loss(inputs, targets)

        # Get probabilities for the target class
        pt = torch.exp(-ce_loss)

        # Calculate focal weight with epsilon for stability
        focal_weight = (1 - pt + self.epsilon) ** self.gamma

        # Calculate focal loss
        focal_loss = focal_weight * ce_loss

        # Apply reduction
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

# Combined Loss for segmentation and regression
class CombinedLoss(nn.Module):
    def __init__(self, seg_weight=1.0, reg_weight=0.5, class_weights=None, gamma=2.0):
        super(CombinedLoss, self).__init__()
        self.seg_weight = seg_weight
        self.reg_weight = reg_weight

        # Segmentation loss: Focal Loss with optional class weights for better handling of imbalance
        self.seg_criterion = FocalLoss(alpha=class_weights, gamma=gamma)

        # Regression loss: Smooth L1 Loss for robustness
        self.reg_criterion = nn.SmoothL1Loss()

    def forward(self, seg_pred, seg_target, reg_pred, reg_target):
        # Handle NaN values
        seg_pred = torch.nan_to_num(seg_pred)
        reg_pred = torch.nan_to_num(reg_pred)

        # Calculate losses
        seg_loss = self.seg_criterion(seg_pred, seg_target)
        reg_loss = self.reg_criterion(reg_pred, reg_target)

        # Combine losses
        combined_loss = self.seg_weight * seg_loss + self.reg_weight * reg_loss

        return combined_loss, seg_loss, reg_loss
