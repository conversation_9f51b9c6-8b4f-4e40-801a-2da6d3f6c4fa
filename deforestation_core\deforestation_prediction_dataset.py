import os
import numpy as np
import torch
from torch.utils.data import Dataset
import rasterio
from PIL import Image
import torchvision.transforms as T
import random
import warnings
from rasterio.errors import NotGeoreferencedWarning

# Suppress rasterio warnings
warnings.filterwarnings("ignore", category=NotGeoreferencedWarning)

# Function to calculate NDVI from satellite image
def calculate_ndvi_from_satellite(satellite_path):
    """
    Calculate NDVI from a satellite image.

    Args:
        satellite_path (str): Path to the satellite image

    Returns:
        numpy.ndarray: NDVI array
    """
    with rasterio.open(satellite_path) as src:
        # Read red and NIR bands
        red = src.read(1).astype('float32')
        nir = src.read(4).astype('float32')

        # Calculate NDVI
        np.seterr(divide='ignore', invalid='ignore')
        ndvi = np.where(
            (nir + red) > 0,
            (nir - red) / (nir + red),
            0
        )

        # Clip to valid range
        ndvi = np.clip(ndvi, -1.0, 1.0)

        return ndvi

# Define SatelliteAugmentation class here
class SatelliteAugmentation:
    def __init__(self):
        pass

    def random_flip(self, img, mask=None, distance=None):
        # Random horizontal flip
        if random.random() > 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
            if mask is not None:
                mask = mask.transpose(Image.FLIP_LEFT_RIGHT)
            if distance is not None:
                distance = distance.transpose(Image.FLIP_LEFT_RIGHT)

        # Random vertical flip
        if random.random() > 0.5:
            img = img.transpose(Image.FLIP_TOP_BOTTOM)
            if mask is not None:
                mask = mask.transpose(Image.FLIP_TOP_BOTTOM)
            if distance is not None:
                distance = distance.transpose(Image.FLIP_TOP_BOTTOM)

        return img, mask, distance

    def random_rotate(self, img, mask=None, distance=None):
        # Random rotation (0, 90, 180, 270 degrees)
        angle = random.choice([0, 90, 180, 270])
        if angle > 0:
            img = img.rotate(angle)
            if mask is not None:
                mask = mask.rotate(angle)
            if distance is not None:
                distance = distance.rotate(angle)

        return img, mask, distance

    def random_brightness_contrast(self, img):
        # Convert to numpy array
        img_np = np.array(img).astype(np.float32)

        # Random brightness
        brightness_factor = random.uniform(0.8, 1.2)
        img_np = img_np * brightness_factor

        # Random contrast
        contrast_factor = random.uniform(0.8, 1.2)
        mean = np.mean(img_np, axis=(0, 1), keepdims=True)
        img_np = (img_np - mean) * contrast_factor + mean

        # Clip values to valid range
        img_np = np.clip(img_np, 0, 255).astype(np.uint8)

        return Image.fromarray(img_np)

    def random_noise(self, img):
        # Convert to numpy array
        img_np = np.array(img).astype(np.float32)

        # Add random noise
        noise_factor = random.uniform(0.01, 0.05)
        noise = np.random.normal(0, noise_factor * 255, img_np.shape)
        img_np = img_np + noise

        # Clip values to valid range
        img_np = np.clip(img_np, 0, 255).astype(np.uint8)

        return Image.fromarray(img_np)

class DeforestationPredictionDataset(Dataset):
    def __init__(self, csv_file, transform_input=None, transform_target=None, transform_distance=None,
                 use_augmentation=False, use_satellite=False, time_periods=None):
        """
        Dataset for training a model to predict future deforestation from sequences of NDVI images.

        Args:
            csv_file (str): Path to the CSV file with annotations.
            transform_input (callable, optional): Transform to be applied on the input images.
            transform_target (callable, optional): Transform to be applied on the target masks.
            transform_distance (callable, optional): Transform to be applied on the distance maps.
            use_augmentation (bool): Whether to use data augmentation.
            use_satellite (bool): Whether to include raw satellite imagery.
            time_periods (list, optional): List of time periods to use. If None, use all available time periods.
        """
        import pandas as pd
        self.df = pd.read_csv(csv_file)
        self.transform_input = transform_input
        self.transform_target = transform_target
        self.transform_distance = transform_distance
        self.use_augmentation = use_augmentation
        self.use_satellite = use_satellite

        # Define all time periods
        self.all_time_periods = ["2015_2016", "2017_2018", "2019_2020", "2021_2022", "2023_2024"]

        # Use specified time periods or all available
        self.time_periods = time_periods if time_periods else self.all_time_periods

        # Define transitions between time periods
        self.transitions = []
        for i in range(len(self.time_periods) - 1):
            self.transitions.append(f"{self.time_periods[i]}_to_{self.time_periods[i+1]}")

        # Use the SatelliteAugmentation class defined above
        self.augmentation = SatelliteAugmentation()

        # Import indices calculator
        try:
            from improved_model_v2 import SpectralIndicesCalculator
            self.indices_calculator = SpectralIndicesCalculator()
        except ImportError:
            print("Warning: SpectralIndicesCalculator not found. Using default NDVI calculation.")
            self.indices_calculator = None

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        # Get file paths
        row = self.df.iloc[idx]

        # Initialize lists to store data for all time periods
        ndvi_tensors = []
        satellite_tensors = []

        # Initialize lists for transitions between time periods
        mask_tensors = []
        distance_tensors = []
        ndvi_diff_tensors = []

        # Process each time period
        for time_period in self.time_periods:
            # Get satellite and NDVI paths for this time period
            satellite_path = row.get(f'satellite_path_{time_period}')
            ndvi_path = row.get(f'ndvi_path_{time_period}')

            # Load NDVI image for this time period
            pil_ndvi = self._load_ndvi_image(ndvi_path, satellite_path)

            # Apply transforms
            if self.transform_input:
                ndvi_tensor = self.transform_input(pil_ndvi)
            else:
                ndvi_tensor = T.ToTensor()(pil_ndvi)

            # Add to list of NDVI tensors
            ndvi_tensors.append(ndvi_tensor)

            # Load satellite image if requested
            if self.use_satellite and satellite_path:
                satellite_tensor = self._load_satellite_image(satellite_path)
                if satellite_tensor is not None:
                    satellite_tensors.append(satellite_tensor)

        # Process transitions between time periods
        for i, transition in enumerate(self.transitions):
            # Get paths for this transition
            mask_path = row.get(f'ternary_mask_path_{transition}')
            distance_path = row.get(f'distance_map_path_{transition}')
            ndvi_diff_path = row.get(f'ndvi_diff_path_{transition}')

            # Load mask, distance map, and NDVI difference
            pil_mask = self._load_mask(mask_path)
            pil_distance = self._load_distance_map(distance_path)
            pil_ndvi_diff = self._load_ndvi_diff(ndvi_diff_path)

            # Apply transforms
            if self.transform_target:
                mask_tensor = self.transform_target(pil_mask)
            else:
                mask_tensor = torch.from_numpy(np.array(pil_mask)).long()

            if self.transform_distance:
                distance_tensor = self.transform_distance(pil_distance)
            else:
                distance_tensor = T.ToTensor()(pil_distance)

            ndvi_diff_tensor = T.ToTensor()(pil_ndvi_diff)

            # Add to lists
            mask_tensors.append(mask_tensor)
            distance_tensors.append(distance_tensor)
            ndvi_diff_tensors.append(ndvi_diff_tensor)

        # Create sample dictionary with sequence data
        sample = {
            'ndvi_sequence': torch.stack(ndvi_tensors) if ndvi_tensors else torch.zeros(len(self.time_periods), 3, 224, 224),
            'mask_sequence': torch.stack(mask_tensors) if mask_tensors else torch.zeros(len(self.transitions), 224, 224, dtype=torch.long),
            'distance_sequence': torch.stack(distance_tensors) if distance_tensors else torch.zeros(len(self.transitions), 1, 224, 224),
            'ndvi_diff_sequence': torch.stack(ndvi_diff_tensors) if ndvi_diff_tensors else torch.zeros(len(self.transitions), 1, 224, 224),
            'file_path': row.get(f'satellite_path_{self.time_periods[0]}', '')
        }

        # Add satellite sequence if available
        if self.use_satellite and satellite_tensors and len(satellite_tensors) == len(self.time_periods):
            sample['satellite_sequence'] = torch.stack(satellite_tensors)

        # Calculate deforestation percentage for each transition (for regression target)
        deforestation_percents = []
        for mask_tensor in mask_tensors:
            deforestation_percent = (mask_tensor == 0).float().mean().item() * 100
            deforestation_percents.append(deforestation_percent)

        if deforestation_percents:
            sample['regression_targets'] = torch.tensor(deforestation_percents, dtype=torch.float32)
        else:
            sample['regression_targets'] = torch.zeros(len(self.transitions), dtype=torch.float32)

        return sample

    def _load_ndvi_image(self, ndvi_path, satellite_path=None):
        """Load NDVI image from path or calculate from satellite image."""
        # Try to load the NDVI image directly first
        try:
            if ndvi_path and os.path.exists(ndvi_path):
                with rasterio.open(ndvi_path) as src:
                    ndvi = src.read(1).astype(np.float32)

                    # Normalize NDVI to 0-1 range
                    ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
                    ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
                    ndvi_norm = np.clip(ndvi_norm, 0, 1)

                    # Convert to RGB image (for model input)
                    ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

                    # Green channel represents vegetation (high NDVI)
                    ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

                    # Red for very low NDVI (potential deforestation)
                    ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

                    # Blue can be constant or another representation
                    ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)

                    return Image.fromarray(ndvi_rgb)
        except Exception as e:
            if ndvi_path:
                print(f"Error loading NDVI image {ndvi_path}: {e}. Falling back to satellite image.")

        # Fall back to calculating NDVI from satellite image
        if satellite_path and os.path.exists(satellite_path):
            try:
                with rasterio.open(satellite_path) as src:
                    satellite = src.read()

                    # Calculate NDVI from satellite bands
                    # Assuming band 4 is NIR and band 3 is Red (common in Sentinel-2)
                    if satellite.shape[0] >= 4:
                        red_band = satellite[2].astype(np.float32)
                        nir_band = satellite[3].astype(np.float32)

                        # Calculate NDVI
                        ndvi = (nir_band - red_band) / (nir_band + red_band + 1e-6)

                        # Handle NaN values
                        ndvi = np.nan_to_num(ndvi, nan=0.0)

                        # Normalize NDVI to 0-1 range
                        ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
                        ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
                        ndvi_norm = np.clip(ndvi_norm, 0, 1)

                        # Convert to RGB image (for model input)
                        ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

                        # Green channel represents vegetation (high NDVI)
                        ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

                        # Red for very low NDVI (potential deforestation)
                        ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

                        # Blue can be constant or another representation
                        ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)
                    else:
                        # If we don't have enough bands, use the first 3 bands as RGB
                        if satellite.shape[0] >= 3:
                            rgb = satellite[0:3]
                            rgb = np.transpose(rgb, (1, 2, 0))
                            rgb_norm = (rgb - np.min(rgb)) / (np.max(rgb) - np.min(rgb) + 1e-6) * 255
                            ndvi_rgb = rgb_norm.astype(np.uint8)
                        else:
                            # Use a single band as grayscale
                            gray = satellite[0]
                            gray_norm = (gray - np.min(gray)) / (np.max(gray) - np.min(gray) + 1e-6) * 255
                            ndvi_rgb = np.stack([gray_norm, gray_norm, gray_norm], axis=-1).astype(np.uint8)

                    return Image.fromarray(ndvi_rgb)
            except Exception as e:
                print(f"Error loading satellite image {satellite_path}: {e}")

        # Create a blank image as fallback
        return Image.new('RGB', (224, 224), color=(0, 0, 0))

    def _load_satellite_image(self, satellite_path):
        """Load satellite image from path."""
        if not satellite_path or not os.path.exists(satellite_path):
            return None

        try:
            with rasterio.open(satellite_path) as src:
                satellite = src.read()

                # Handle different band combinations
                if satellite.shape[0] >= 3:
                    # Use RGB bands
                    rgb_satellite = satellite[0:3, :, :]
                    # Transpose to [H, W, C] for PIL
                    rgb_satellite = np.transpose(rgb_satellite, (1, 2, 0))
                    # Normalize to 0-255 range
                    rgb_min, rgb_max = np.min(rgb_satellite), np.max(rgb_satellite)
                    rgb_norm = (rgb_satellite - rgb_min) / (rgb_max - rgb_min + 1e-6) * 255
                    rgb_norm = np.clip(rgb_norm, 0, 255).astype(np.uint8)

                    pil_satellite = Image.fromarray(rgb_norm)
                else:
                    # Single band, use as grayscale
                    pil_satellite = Image.fromarray((satellite[0] * 255 / satellite[0].max()).astype(np.uint8))

                # Apply transforms if provided
                if self.transform_input:
                    satellite_tensor = self.transform_input(pil_satellite)
                else:
                    satellite_tensor = T.ToTensor()(pil_satellite)

                return satellite_tensor
        except Exception as e:
            print(f"Error loading satellite image {satellite_path}: {e}")
            return None

    def _load_mask(self, mask_path):
        """Load ternary mask from path."""
        if not mask_path or not os.path.exists(mask_path):
            return Image.new('L', (224, 224), color=1)  # Default to stable class

        try:
            with rasterio.open(mask_path) as src:
                mask = src.read(1).astype(np.int64)

                # Ensure the mask uses the correct mapping:
                # 0 (Deforestation)
                # 1 (Stable)
                # 2 (Growth)
                # Check unique values in the mask
                unique_values = np.unique(mask)

                # Remap values if needed
                if -1 in unique_values:
                    # Convert from -1, 0, 1 to 0, 1, 2
                    mask_remapped = np.zeros_like(mask)
                    mask_remapped[mask == -1] = 0  # Deforestation
                    mask_remapped[mask == 0] = 1   # Stable
                    mask_remapped[mask == 1] = 2   # Growth
                else:
                    # Ensure all values are in range 0-2
                    mask_remapped = np.clip(mask, 0, 2)

                # Resize mask to 224x224 for model input
                mask_remapped = mask_remapped.astype(np.uint8)
                pil_mask = Image.fromarray(mask_remapped)
                pil_mask = pil_mask.resize((224, 224), Image.NEAREST)
                return pil_mask
        except Exception as e:
            print(f"Error loading mask {mask_path}: {e}")
            # Create a blank mask as fallback (default to stable class)
            return Image.new('L', (224, 224), color=1)

    def _load_distance_map(self, distance_path):
        """Load distance map from path."""
        if not distance_path or not os.path.exists(distance_path):
            return Image.new('L', (224, 224), color=0)

        try:
            with rasterio.open(distance_path) as src:
                distance = src.read(1).astype(np.float32)

                # Normalize distance to 0-1 range
                distance_max = np.max(distance)
                distance_norm = distance / (distance_max + 1e-6)

                # Resize distance map to 224x224 for model input
                distance_norm = (distance_norm * 255).astype(np.uint8)
                pil_distance = Image.fromarray(distance_norm, mode='L')
                pil_distance = pil_distance.resize((224, 224), Image.BILINEAR)
                return pil_distance
        except Exception as e:
            print(f"Error loading distance map {distance_path}: {e}")
            # Create a blank distance map as fallback
            return Image.new('L', (224, 224), color=0)

    def _load_ndvi_diff(self, ndvi_diff_path):
        """Load NDVI difference from path."""
        if not ndvi_diff_path or not os.path.exists(ndvi_diff_path):
            return Image.new('L', (224, 224), color=128)  # Middle gray for no difference

        try:
            with rasterio.open(ndvi_diff_path) as src:
                ndvi_diff = src.read(1).astype(np.float32)

                # Normalize to 0-1 range for visualization
                ndvi_diff_min, ndvi_diff_max = -1.0, 1.0
                ndvi_diff_norm = (ndvi_diff - ndvi_diff_min) / (ndvi_diff_max - ndvi_diff_min)
                ndvi_diff_norm = np.clip(ndvi_diff_norm, 0, 1)

                # Resize NDVI difference to 224x224 for model input
                ndvi_diff_norm = (ndvi_diff_norm * 255).astype(np.uint8)
                pil_ndvi_diff = Image.fromarray(ndvi_diff_norm, mode='L')
                pil_ndvi_diff = pil_ndvi_diff.resize((224, 224), Image.BILINEAR)
                return pil_ndvi_diff
        except Exception as e:
            print(f"Error loading NDVI difference {ndvi_diff_path}: {e}")
            # Create a blank image as fallback
            return Image.new('L', (224, 224), color=128)  # Middle gray for no difference

# Custom collate function for sequence data
def custom_prediction_collate(batch):
    """Custom collate function to handle sequence data and variable-sized tensors."""
    # Initialize empty dictionary for batch
    batch_dict = {}

    # Get all keys from the first sample
    keys = batch[0].keys()

    # Process each key
    for key in keys:
        # Get all values for this key, handling missing keys
        values = []
        for sample in batch:
            if key in sample:
                values.append(sample[key])
            elif key == 'satellite_sequence':
                # For missing satellite images, use the NDVI sequence instead
                if 'ndvi_sequence' in sample:
                    values.append(sample['ndvi_sequence'])
                else:
                    # Create a blank tensor as fallback
                    values.append(torch.zeros(5, 3, 224, 224))  # Assuming 5 time periods
            else:
                # Skip samples with missing keys
                continue

        # Skip empty keys
        if not values:
            continue

        # Handle tensors
        if isinstance(values[0], torch.Tensor):
            # Check if all tensors have the same shape
            shapes = [v.shape for v in values]
            if len(set(str(s) for s in shapes)) == 1:
                # All tensors have the same shape, stack them
                batch_dict[key] = torch.stack(values)
            else:
                # Tensors have different shapes
                if key == 'ndvi_sequence' or key == 'satellite_sequence':
                    # Resize all sequence tensors to [T, 3, 224, 224]
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 4:  # [T, C, H, W]
                            # Process each time step
                            time_steps = []
                            for t in range(v.shape[0]):
                                time_step = v[t]
                                time_step = time_step.unsqueeze(0)  # Add batch dimension
                                time_step = torch.nn.functional.interpolate(
                                    time_step,
                                    size=(224, 224),
                                    mode='bilinear',
                                    align_corners=False
                                )
                                time_step = time_step.squeeze(0)  # Remove batch dimension
                                time_steps.append(time_step)
                            resized_values.append(torch.stack(time_steps))
                        else:
                            # Handle unexpected shape
                            print(f"Warning: Unexpected shape {v.shape} for {key}")
                            # Create a blank tensor as fallback
                            resized_values.append(torch.zeros(5, 3, 224, 224))  # Assuming 5 time periods
                    batch_dict[key] = torch.stack(resized_values)
                elif key == 'mask_sequence':
                    # Resize all mask sequence tensors to [T, 224, 224]
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 3:  # [T, H, W]
                            # Process each time step
                            time_steps = []
                            for t in range(v.shape[0]):
                                time_step = v[t]
                                time_step = time_step.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
                                time_step = torch.nn.functional.interpolate(
                                    time_step.float(),
                                    size=(224, 224),
                                    mode='nearest'
                                )
                                time_step = time_step.squeeze(0).squeeze(0).long()  # Remove batch and channel dimensions
                                time_steps.append(time_step)
                            resized_values.append(torch.stack(time_steps))
                        else:
                            # Handle unexpected shape
                            print(f"Warning: Unexpected shape {v.shape} for {key}")
                            # Create a blank tensor as fallback
                            resized_values.append(torch.zeros(4, 224, 224, dtype=torch.long))  # Assuming 4 transitions
                    batch_dict[key] = torch.stack(resized_values)
                elif key in ['distance_sequence', 'ndvi_diff_sequence']:
                    # Resize all distance and ndvi_diff sequence tensors to [T, 1, 224, 224]
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 4:  # [T, 1, H, W]
                            # Process each time step
                            time_steps = []
                            for t in range(v.shape[0]):
                                time_step = v[t]
                                time_step = time_step.unsqueeze(0)  # Add batch dimension
                                time_step = torch.nn.functional.interpolate(
                                    time_step,
                                    size=(224, 224),
                                    mode='bilinear',
                                    align_corners=False
                                )
                                time_step = time_step.squeeze(0)  # Remove batch dimension
                                time_steps.append(time_step)
                            resized_values.append(torch.stack(time_steps))
                        else:
                            # Handle unexpected shape
                            print(f"Warning: Unexpected shape {v.shape} for {key}")
                            # Create a blank tensor as fallback
                            resized_values.append(torch.zeros(4, 1, 224, 224))  # Assuming 4 transitions
                    batch_dict[key] = torch.stack(resized_values)
                else:
                    # For other tensors, just stack them
                    try:
                        batch_dict[key] = torch.stack(values)
                    except:
                        print(f"Warning: Could not stack tensors with shapes {shapes} for {key}")
                        # Try to convert to same shape
                        if all(len(v.shape) == 1 for v in values):
                            # For 1D tensors, pad to the maximum length
                            max_len = max(v.shape[0] for v in values)
                            padded_values = []
                            for v in values:
                                if v.shape[0] < max_len:
                                    padding = torch.zeros(max_len - v.shape[0], dtype=v.dtype, device=v.device)
                                    v = torch.cat([v, padding])
                                padded_values.append(v)
                            batch_dict[key] = torch.stack(padded_values)
                        else:
                            # For other cases, just use the first shape
                            target_shape = values[0].shape
                            resized_values = []
                            for v in values:
                                if v.shape != target_shape:
                                    # Try to resize
                                    try:
                                        v = v.view(target_shape)
                                    except:
                                        # If that fails, create a new tensor
                                        v = torch.zeros(target_shape, dtype=v.dtype, device=v.device)
                                resized_values.append(v)
                            batch_dict[key] = torch.stack(resized_values)
        else:
            # For file paths or other non-tensor values, just create a list
            batch_dict[key] = values

    return batch_dict
