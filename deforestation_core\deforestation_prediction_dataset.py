import os
import numpy as np
import torch
from torch.utils.data import Dataset
import rasterio
from PIL import Image
import torchvision.transforms as T
import random
import warnings
from rasterio.errors import NotGeoreferencedWarning

# Suppress rasterio warnings
warnings.filterwarnings("ignore", category=NotGeoreferencedWarning)

# Function to calculate NDVI from satellite image
def calculate_ndvi_from_satellite(satellite_path):
    """
    Calculate NDVI from a satellite image.

    Args:
        satellite_path (str): Path to the satellite image

    Returns:
        numpy.ndarray: NDVI array
    """
    with rasterio.open(satellite_path) as src:
        # Read red and NIR bands
        red = src.read(1).astype('float32')
        nir = src.read(4).astype('float32')

        # Calculate NDVI
        np.seterr(divide='ignore', invalid='ignore')
        ndvi = np.where(
            (nir + red) > 0,
            (nir - red) / (nir + red),
            0
        )

        # Clip to valid range
        ndvi = np.clip(ndvi, -1.0, 1.0)

        return ndvi

# Define SatelliteAugmentation class here
class SatelliteAugmentation:
    def __init__(self):
        pass

    def random_flip(self, img, mask=None, distance=None):
        # Random horizontal flip
        if random.random() > 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
            if mask is not None:
                mask = mask.transpose(Image.FLIP_LEFT_RIGHT)
            if distance is not None:
                distance = distance.transpose(Image.FLIP_LEFT_RIGHT)

        # Random vertical flip
        if random.random() > 0.5:
            img = img.transpose(Image.FLIP_TOP_BOTTOM)
            if mask is not None:
                mask = mask.transpose(Image.FLIP_TOP_BOTTOM)
            if distance is not None:
                distance = distance.transpose(Image.FLIP_TOP_BOTTOM)

        return img, mask, distance

    def random_rotate(self, img, mask=None, distance=None):
        # Random rotation (0, 90, 180, 270 degrees)
        angle = random.choice([0, 90, 180, 270])
        if angle > 0:
            img = img.rotate(angle)
            if mask is not None:
                mask = mask.rotate(angle)
            if distance is not None:
                distance = distance.rotate(angle)

        return img, mask, distance

    def random_brightness_contrast(self, img):
        # Convert to numpy array
        img_np = np.array(img).astype(np.float32)

        # Random brightness
        brightness_factor = random.uniform(0.8, 1.2)
        img_np = img_np * brightness_factor

        # Random contrast
        contrast_factor = random.uniform(0.8, 1.2)
        mean = np.mean(img_np, axis=(0, 1), keepdims=True)
        img_np = (img_np - mean) * contrast_factor + mean

        # Clip values to valid range
        img_np = np.clip(img_np, 0, 255).astype(np.uint8)

        return Image.fromarray(img_np)

    def random_noise(self, img):
        # Convert to numpy array
        img_np = np.array(img).astype(np.float32)

        # Add random noise
        noise_factor = random.uniform(0.01, 0.05)
        noise = np.random.normal(0, noise_factor * 255, img_np.shape)
        img_np = img_np + noise

        # Clip values to valid range
        img_np = np.clip(img_np, 0, 255).astype(np.uint8)

        return Image.fromarray(img_np)

class DeforestationPredictionDataset(Dataset):
    def __init__(self, csv_file, transform_input=None, transform_target=None, transform_distance=None,
                 use_augmentation=False, use_satellite=False):
        """
        Dataset for training a model to predict future deforestation from current NDVI images.

        Args:
            csv_file (str): Path to the CSV file with annotations.
            transform_input (callable, optional): Transform to be applied on the input images.
            transform_target (callable, optional): Transform to be applied on the target masks.
            transform_distance (callable, optional): Transform to be applied on the distance maps.
            use_augmentation (bool): Whether to use data augmentation.
            use_satellite (bool): Whether to include raw satellite imagery.
        """
        import pandas as pd
        self.df = pd.read_csv(csv_file)
        self.transform_input = transform_input
        self.transform_target = transform_target
        self.transform_distance = transform_distance
        self.use_augmentation = use_augmentation
        self.use_satellite = use_satellite

        # Use the SatelliteAugmentation class defined above
        self.augmentation = SatelliteAugmentation()

        # Import indices calculator
        from improved_model_v2 import SpectralIndicesCalculator
        self.indices_calculator = SpectralIndicesCalculator()

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        # Get file paths
        row = self.df.iloc[idx]

        # Get file paths
        mask_path = row['ternary_mask_path']  # Target
        distance_path = row['distance_map_path']  # Distance map
        ndvi_diff_path = row['ndvi_diff_path']  # NDVI difference
        satellite_path = row['satellite_path_t1']  # Satellite image
        ndvi_path = row['ndvi_path_t1']  # NDVI image

        # Try to load the NDVI image directly first
        try:
            with rasterio.open(ndvi_path) as src:
                ndvi = src.read(1).astype(np.float32)

                # Normalize NDVI to 0-1 range
                ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
                ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
                ndvi_norm = np.clip(ndvi_norm, 0, 1)

                # Convert to RGB image (for model input)
                ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

                # Green channel represents vegetation (high NDVI)
                ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

                # Red for very low NDVI (potential deforestation)
                ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

                # Blue can be constant or another representation
                ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)

                pil_ndvi = Image.fromarray(ndvi_rgb)
        except Exception as e:
            print(f"Error loading NDVI image: {e}. Falling back to satellite image.")
            # Fall back to calculating NDVI from satellite image
            try:
                with rasterio.open(satellite_path) as src:
                    satellite = src.read()

                    # Calculate NDVI from satellite bands
                    # Assuming band 4 is NIR and band 3 is Red (common in Sentinel-2)
                    if satellite.shape[0] >= 4:
                        red_band = satellite[2].astype(np.float32)
                        nir_band = satellite[3].astype(np.float32)

                        # Calculate NDVI
                        ndvi = (nir_band - red_band) / (nir_band + red_band + 1e-6)

                        # Handle NaN values
                        ndvi = np.nan_to_num(ndvi, nan=0.0)

                        # Normalize NDVI to 0-1 range
                        ndvi_min, ndvi_max = -1.0, 1.0  # NDVI range
                        ndvi_norm = (ndvi - ndvi_min) / (ndvi_max - ndvi_min)
                        ndvi_norm = np.clip(ndvi_norm, 0, 1)

                        # Convert to RGB image (for model input)
                        ndvi_rgb = np.zeros((ndvi_norm.shape[0], ndvi_norm.shape[1], 3), dtype=np.uint8)

                        # Green channel represents vegetation (high NDVI)
                        ndvi_rgb[:, :, 1] = (ndvi_norm * 255).astype(np.uint8)

                        # Red for very low NDVI (potential deforestation)
                        ndvi_rgb[:, :, 0] = ((1 - ndvi_norm) * 255).astype(np.uint8)

                        # Blue can be constant or another representation
                        ndvi_rgb[:, :, 2] = np.zeros_like(ndvi_norm, dtype=np.uint8)
                    else:
                        # If we don't have enough bands, use the first 3 bands as RGB
                        if satellite.shape[0] >= 3:
                            rgb = satellite[0:3]
                            rgb = np.transpose(rgb, (1, 2, 0))
                            rgb_norm = (rgb - np.min(rgb)) / (np.max(rgb) - np.min(rgb) + 1e-6) * 255
                            ndvi_rgb = rgb_norm.astype(np.uint8)
                        else:
                            # Use a single band as grayscale
                            gray = satellite[0]
                            gray_norm = (gray - np.min(gray)) / (np.max(gray) - np.min(gray) + 1e-6) * 255
                            ndvi_rgb = np.stack([gray_norm, gray_norm, gray_norm], axis=-1).astype(np.uint8)

                    pil_ndvi = Image.fromarray(ndvi_rgb)
            except Exception as e:
                print(f"Error loading satellite image: {e}")
                # Create a blank image as fallback
                pil_ndvi = Image.new('RGB', (224, 224), color=(0, 0, 0))

        # Load segmentation mask (target)
        try:
            with rasterio.open(mask_path) as src:
                mask = src.read(1).astype(np.int64)

                # Ensure the mask uses the correct mapping:
                # 0 (Deforestation)
                # 1 (Stable)
                # 2 (Growth)
                # Check unique values in the mask
                unique_values = np.unique(mask)

                # Remap values if needed
                if -1 in unique_values:
                    # Convert from -1, 0, 1 to 0, 1, 2
                    mask_remapped = np.zeros_like(mask)
                    mask_remapped[mask == -1] = 0  # Deforestation
                    mask_remapped[mask == 0] = 1   # Stable
                    mask_remapped[mask == 1] = 2   # Growth
                else:
                    # Ensure all values are in range 0-2
                    mask_remapped = np.clip(mask, 0, 2)

                # Resize mask to 224x224 for model input
                mask_remapped = mask_remapped.astype(np.uint8)
                pil_mask = Image.fromarray(mask_remapped)
                pil_mask = pil_mask.resize((224, 224), Image.NEAREST)
        except Exception as e:
            print(f"Error loading mask: {e}")
            # Create a blank mask as fallback
            pil_mask = Image.new('L', (224, 224), color=0)

        # Load distance map
        try:
            with rasterio.open(distance_path) as src:
                distance = src.read(1).astype(np.float32)

                # Normalize distance to 0-1 range
                distance_max = np.max(distance)
                distance_norm = distance / (distance_max + 1e-6)

                # Resize distance map to 224x224 for model input
                distance_norm = (distance_norm * 255).astype(np.uint8)
                pil_distance = Image.fromarray(distance_norm, mode='L')
                pil_distance = pil_distance.resize((224, 224), Image.BILINEAR)
        except Exception as e:
            print(f"Error loading distance map: {e}")
            # Create a blank distance map as fallback
            pil_distance = Image.new('L', (224, 224), color=0)

        # Load NDVI difference (for reference)
        try:
            with rasterio.open(ndvi_diff_path) as src:
                ndvi_diff = src.read(1).astype(np.float32)

                # Normalize to 0-1 range for visualization
                ndvi_diff_min, ndvi_diff_max = -1.0, 1.0
                ndvi_diff_norm = (ndvi_diff - ndvi_diff_min) / (ndvi_diff_max - ndvi_diff_min)
                ndvi_diff_norm = np.clip(ndvi_diff_norm, 0, 1)

                # Resize NDVI difference to 224x224 for model input
                ndvi_diff_norm = (ndvi_diff_norm * 255).astype(np.uint8)
                pil_ndvi_diff = Image.fromarray(ndvi_diff_norm, mode='L')
                pil_ndvi_diff = pil_ndvi_diff.resize((224, 224), Image.BILINEAR)
        except Exception as e:
            print(f"Error loading NDVI difference: {e}")
            # Create a blank image as fallback
            pil_ndvi_diff = Image.new('L', (224, 224), color=0)

        # Load satellite image (optional)
        satellite_tensor = None
        if self.use_satellite and satellite_path:
            try:
                with rasterio.open(satellite_path) as src:
                    satellite = src.read()

                    # Handle different band combinations
                    if satellite.shape[0] >= 3:
                        # Use RGB bands
                        rgb_satellite = satellite[0:3, :, :]
                        # Transpose to [H, W, C] for PIL
                        rgb_satellite = np.transpose(rgb_satellite, (1, 2, 0))
                        # Normalize to 0-255 range
                        rgb_min, rgb_max = np.min(rgb_satellite), np.max(rgb_satellite)
                        rgb_norm = (rgb_satellite - rgb_min) / (rgb_max - rgb_min + 1e-6) * 255
                        rgb_norm = np.clip(rgb_norm, 0, 255).astype(np.uint8)

                        pil_satellite = Image.fromarray(rgb_norm)
                    else:
                        # Single band, use as grayscale
                        pil_satellite = Image.fromarray((satellite[0] * 255 / satellite[0].max()).astype(np.uint8))

                    # Apply transforms if provided
                    if self.transform_input:
                        satellite_tensor = self.transform_input(pil_satellite)
                    else:
                        satellite_tensor = T.ToTensor()(pil_satellite)
            except Exception as e:
                print(f"Error loading satellite image: {e}")
                satellite_tensor = None

        # Apply augmentation if enabled
        if self.use_augmentation:
            # Apply random flips and rotations
            pil_ndvi, pil_mask, pil_distance = self.augmentation.random_flip(pil_ndvi, pil_mask, pil_distance)
            pil_ndvi, pil_mask, pil_distance = self.augmentation.random_rotate(pil_ndvi, pil_mask, pil_distance)

            # Apply random brightness/contrast to input image
            pil_ndvi = self.augmentation.random_brightness_contrast(pil_ndvi)

            # Apply random noise
            if random.random() > 0.5:
                pil_ndvi = self.augmentation.random_noise(pil_ndvi)

        # Apply transforms
        if self.transform_input:
            input_tensor = self.transform_input(pil_ndvi)
        else:
            input_tensor = T.ToTensor()(pil_ndvi)

        if self.transform_target:
            target_tensor = self.transform_target(pil_mask)
        else:
            target_tensor = torch.from_numpy(np.array(pil_mask)).long()

        if self.transform_distance:
            distance_tensor = self.transform_distance(pil_distance)
        else:
            distance_tensor = T.ToTensor()(pil_distance)

        # Create NDVI difference tensor
        ndvi_diff_tensor = T.ToTensor()(pil_ndvi_diff)

        # Calculate deforestation percentage (for regression target)
        deforestation_percent = (target_tensor == 0).float().mean().item() * 100

        # Create sample dictionary
        sample = {
            'input': input_tensor,
            'target': target_tensor,
            'regression_target': torch.tensor(deforestation_percent, dtype=torch.float32),
            'distance': distance_tensor,
            'ndvi_diff': ndvi_diff_tensor,
            'file_path': row['satellite_path_t1']
        }

        # Add satellite tensor if available
        if satellite_tensor is not None:
            sample['satellite'] = satellite_tensor

        return sample

# Custom collate function (reuse from improved_deforestation_training.py)
def custom_prediction_collate(batch):
    """Custom collate function to handle variable-sized tensors and missing values."""
    # Initialize empty dictionary for batch
    batch_dict = {}

    # Get all keys from the first sample
    keys = batch[0].keys()

    # Process each key
    for key in keys:
        # Get all values for this key, handling missing keys
        values = []
        for sample in batch:
            if key in sample:
                values.append(sample[key])
            elif key == 'satellite':
                # For missing satellite images, use the NDVI input instead
                if 'input' in sample:
                    values.append(sample['input'])
                else:
                    # Create a blank tensor as fallback
                    values.append(torch.zeros(3, 224, 224))
            else:
                # Skip samples with missing keys
                continue

        # Skip empty keys
        if not values:
            continue

        # Handle tensors
        if isinstance(values[0], torch.Tensor):
            # Check if all tensors have the same shape
            shapes = [v.shape for v in values]
            if len(set(str(s) for s in shapes)) == 1:
                # All tensors have the same shape, stack them
                batch_dict[key] = torch.stack(values)
            else:
                # Tensors have different shapes
                if key == 'input' or key == 'satellite':
                    # Resize all input tensors to 224x224
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 3:  # [C, H, W]
                            v = v.unsqueeze(0)  # Add batch dimension
                            v = torch.nn.functional.interpolate(
                                v,
                                size=(224, 224),
                                mode='bilinear',
                                align_corners=False
                            )
                            v = v.squeeze(0)  # Remove batch dimension
                        resized_values.append(v)
                    batch_dict[key] = torch.stack(resized_values)
                elif key == 'target':
                    # Resize all target tensors to 224x224
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 2:  # [H, W]
                            v = v.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
                            v = torch.nn.functional.interpolate(
                                v.float(),
                                size=(224, 224),
                                mode='nearest'
                            )
                            v = v.squeeze(0).squeeze(0).long()  # Remove batch and channel dimensions
                        resized_values.append(v)
                    batch_dict[key] = torch.stack(resized_values)
                elif key in ['distance', 'ndvi_diff']:
                    # Resize all distance and ndvi_diff tensors to 224x224
                    resized_values = []
                    for v in values:
                        if len(v.shape) == 3:  # [1, H, W]
                            v = v.unsqueeze(0)  # Add batch dimension
                            v = torch.nn.functional.interpolate(
                                v,
                                size=(224, 224),
                                mode='bilinear',
                                align_corners=False
                            )
                            v = v.squeeze(0)  # Remove batch dimension
                        resized_values.append(v)
                    batch_dict[key] = torch.stack(resized_values)
                else:
                    # For other tensors, just stack them
                    batch_dict[key] = torch.stack(values)
        else:
            # For file paths or other non-tensor values, just create a list
            batch_dict[key] = values

    return batch_dict
